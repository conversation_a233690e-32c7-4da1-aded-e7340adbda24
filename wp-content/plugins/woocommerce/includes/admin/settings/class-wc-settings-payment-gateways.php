<?php
/**
 * WooCommerce Checkout Settings
 *
 * @package WooCommerce\Admin
 */

declare( strict_types = 1 );

use Automattic\WooCommerce\Internal\Admin\Loader;

defined( 'ABSPATH' ) || exit;

if ( class_exists( 'WC_Settings_Payment_Gateways', false ) ) {
	return new WC_Settings_Payment_Gateways();
}

/**
 * WC_Settings_Payment_Gateways.
 */
class WC_Settings_Payment_Gateways extends WC_Settings_Page {

	const TAB_NAME = 'checkout';

	const MAIN_SECTION_NAME    = 'main';
	const OFFLINE_SECTION_NAME = 'offline';
	const COD_SECTION_NAME     = 'cod';  // Cash on delivery.
	const BACS_SECTION_NAME    = 'bacs';  // Direct bank transfer.
	const CHEQUE_SECTION_NAME  = 'cheque';  // Cheque payments.

	/**
	 * Setting page icon.
	 *
	 * @var string
	 */
	public $icon = 'payment';

	/**
	 * Memoized list of sections to render using React.
	 *
	 * @var array|null
	 */
	private ?array $reactified_sections_memo = null;

	/**
	 * Constructor.
	 */
	public function __construct() {
		$this->id    = self::TAB_NAME;
		$this->label = esc_html_x( 'Payments', 'Settings tab label', 'woocommerce' );

		// Add filters and actions.
		add_filter( 'admin_body_class', array( $this, 'add_body_classes' ), 30 );
		add_action( 'admin_head', array( $this, 'hide_help_tabs' ) );
		// Hook in as late as possible - `in_admin_header` is the last action before the `admin_notices` action is fired.
		// It is too risky to hook into `admin_notices` with a low priority because the callbacks might be cached.
		add_action( 'in_admin_header', array( $this, 'suppress_admin_notices' ), PHP_INT_MAX );

		// Do not show any store alerts (WC admin notes with type: 'error,update' and status: 'unactioned')
		// on the WooCommerce Payments settings page and Reactified sections.
		add_filter( 'woocommerce_admin_features', array( $this, 'suppress_store_alerts' ), PHP_INT_MAX );

		parent::__construct();
	}

	/**
	 * Check if the given section should be rendered using React.
	 *
	 * @param mixed $section The section name to check.
	 *                       Since this value originates from the global `$current_section` variable,
	 *                       it is best to accept anything and standardize it to a string.
	 *
	 * @return bool Whether the section should be rendered using React.
	 */
	public function should_render_react_section( $section ): bool {
		return in_array( $this->standardize_section_name( $section ), $this->get_reactified_sections(), true );
	}

	/**
	 * Add body classes.
	 *
	 * @param string $classes The existing body classes.
	 *
	 * @return string The modified body classes.
	 */
	public function add_body_classes( $classes ) {
		global $current_tab, $current_section;

		// Bail if the $classes variable is not a string.
		if ( ! is_string( $classes ) ) {
			return $classes;
		}

		// If we are not on the WooCommerce Payments settings page, return the classes as they are.
		if ( self::TAB_NAME !== $current_tab ) {
			return $classes;
		}

		if ( ! $this->should_render_react_section( $current_section ) ) {
			// Add a class to indicate that the payments settings section page is rendered in legacy mode.
			$classes .= ' woocommerce-settings-payments-section_legacy';
			// Add a class to indicate that the current section is rendered in legacy mode.
			$classes .= ' woocommerce_page_wc-settings-checkout-section-' . esc_attr( $this->standardize_section_name( $current_section ) ) . '_legacy';
		}

		return $classes;
	}

	/**
	 * Output the settings.
	 */
	public function output() {
		// phpcs:disable WordPress.Security.NonceVerification.Recommended
		global $current_section;

		// We don't want to output anything from the action for now. So we buffer it and discard it.
		ob_start();
		/**
		 * Fires before the payment gateways settings fields are rendered.
		 *
		 * @since 1.5.7
		 */
		do_action( 'woocommerce_admin_field_payment_gateways' );
		ob_end_clean();

		if ( is_string( $current_section ) && $this->should_render_react_section( $current_section ) ) {
			$this->render_react_section( $this->standardize_section_name( $current_section ) );
		} elseif ( is_string( $current_section ) && ! empty( $current_section ) ) {
			// Load gateways so we can show any global options they may have.
			$payment_gateways = WC()->payment_gateways()->payment_gateways;
			$this->render_classic_gateway_settings_page( $payment_gateways, $current_section );
		} else {
			$this->render_react_section( self::MAIN_SECTION_NAME );
		}

		parent::output();
		//phpcs:enable
	}

	/**
	 * Get settings array.
	 *
	 * This is just for backward compatibility with the rest of the codebase (primarily API responses).
	 *
	 * @return array
	 */
	protected function get_settings_for_default_section() {
		return array(
			array(
				'type' => 'title',
				// this is needed as <table> tag is generated by this element, even if it has no other content.
			),
			array(
				'type' => 'sectionend',
				'id'   => 'payment_gateways_options',
			),
		);
	}

	/**
	 * Get the whitelist of sections to render using React.
	 *
	 * @return array List of section identifiers.
	 */
	private function get_reactified_sections(): array {
		if ( ! is_null( $this->reactified_sections_memo ) ) {
			return $this->reactified_sections_memo;
		}

		// These sections are always rendered using React.
		$reactified_sections = array(
			self::MAIN_SECTION_NAME,
			self::OFFLINE_SECTION_NAME,
		);

		// These sections are optional and can be modified by plugins or themes.
		$optional_reactified_sections = array(
			self::COD_SECTION_NAME,
			self::BACS_SECTION_NAME,
			self::CHEQUE_SECTION_NAME,
		);

		/**
		 * Modify the optional set of payments settings sections to be rendered using React.
		 *
		 * This filter allows plugins to add or remove optional sections (typically offline gateways)
		 * that should be rendered using React. Sections should be identified by their gateway IDs.
		 * Note: The main Payments page ("main") and the Offline overview ("offline") are always React-only
		 * and cannot be disabled via this filter.
		 *
		 * @since 9.3.0
		 *
		 * @param array $sections List of section identifiers to be rendered using React.
		 */
		$optional_reactified_sections = apply_filters( 'experimental_woocommerce_admin_payment_reactify_render_sections', $optional_reactified_sections );
		if ( empty( $optional_reactified_sections ) || ! is_array( $optional_reactified_sections ) ) {
			// Sanity check: use empty array if the filter returns something unexpected.
			$optional_reactified_sections = array();
		} else {
			// Enforce a list format and string-only values for section identifiers.
			$optional_reactified_sections = array_values( array_filter( $optional_reactified_sections, 'is_string' ) );
		}

		$this->reactified_sections_memo = array_unique( array_merge( $reactified_sections, $optional_reactified_sections ) );

		return $this->reactified_sections_memo;
	}

	/**
	 * Standardize the current section name.
	 *
	 * @param mixed $section The section name to standardize.
	 *
	 * @return string The standardized section name.
	 */
	private function standardize_section_name( $section ): string {
		$section = (string) $section;
		// If the section is empty, we are on the main settings page/section. Use a standardized name.
		if ( '' === $section ) {
			return self::MAIN_SECTION_NAME;
		}

		return $section;
	}

	/**
	 * Render the React section.
	 *
	 * @param string $section The section to render.
	 */
	private function render_react_section( string $section ) {
		global $hide_save_button;
		$hide_save_button = true;
		echo '<div id="experimental_wc_settings_payments_' . esc_attr( $section ) . '"></div>';
	}

	/**
	 * Render the classic gateway settings page.
	 *
	 * @param array  $payment_gateways The payment gateways.
	 * @param string $current_section  The current section.
	 */
	private function render_classic_gateway_settings_page( array $payment_gateways, string $current_section ) {
		foreach ( $payment_gateways as $gateway ) {
			if ( in_array( $current_section, array( $gateway->id, sanitize_title( get_class( $gateway ) ) ), true ) ) {
				if ( isset( $_GET['toggle_enabled'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
					$enabled = $gateway->get_option( 'enabled' );

					if ( $enabled ) {
						$gateway->settings['enabled'] = wc_string_to_bool( $enabled ) ? 'no' : 'yes';
					}
				}
				$this->run_gateway_admin_options( $gateway );
				break;
			}
		}
	}

	/**
	 * Run the 'admin_options' method on a given gateway.
	 *
	 * This method exists to help with unit testing.
	 *
	 * @param object $gateway The gateway object to run the method on.
	 */
	protected function run_gateway_admin_options( $gateway ) {
		$gateway->admin_options();
	}

	/**
	 * Get all sections for the current page.
	 *
	 * Reactified section pages won't have any sections.
	 * The rest of the settings pages will get the default/own section and those added via
	 * the `woocommerce_get_sections_checkout` filter.
	 *
	 * @return array The sections for this settings page.
	 */
	public function get_sections() {
		global $current_tab, $current_section;

		// We only want to prevent sections on the main WooCommerce Payments settings page and Reactified sections.
		if ( self::TAB_NAME === $current_tab && $this->should_render_react_section( $current_section ) ) {
			return array();
		}

		return parent::get_sections();
	}

	/**
	 * Save settings.
	 */
	public function save() {
		global $current_section;

		$standardized_section = $this->standardize_section_name( $current_section );

		$wc_payment_gateways = WC_Payment_Gateways::instance();

		$this->save_settings_for_current_section();

		if ( self::MAIN_SECTION_NAME === $standardized_section ) {
			// This makes sure 'gateway ordering' is saved.
			$wc_payment_gateways->process_admin_options();
			$wc_payment_gateways->init();
		} else {
			// This may be a gateway or some custom section.
			foreach ( $wc_payment_gateways->payment_gateways() as $gateway ) {
				// If the section is that of a gateway, we need to run the gateway actions and init.
				if ( in_array( $standardized_section, array( $gateway->id, sanitize_title( get_class( $gateway ) ) ), true ) ) {
					/**
					 * Fires update actions for payment gateways.
					 *
					 * @since 3.4.0
					 *
					 * @param int $gateway->id Gateway ID.
					 */
					do_action( 'woocommerce_update_options_payment_gateways_' . $gateway->id );
					$wc_payment_gateways->init();

					// There is no need to run the action and gateways init again
					// since we can't be on the section page of multiple gateways at once.
					break;
				}
			}

			$this->do_update_options_action();
		}
	}

	/**
	 * Hide the help tabs.
	 */
	public function hide_help_tabs() {
		global $current_tab, $current_section;

		$screen = get_current_screen();
		if ( ! $screen instanceof WP_Screen || 'woocommerce_page_wc-settings' !== $screen->id ) {
			return;
		}

		// We only want to hide the help tabs on the main WooCommerce Payments settings page and Reactified sections.
		if ( self::TAB_NAME !== $current_tab ) {
			return;
		}
		if ( ! $this->should_render_react_section( $current_section ) ) {
			return;
		}

		$screen->remove_help_tabs();
	}

	/**
	 * Suppress WP admin notices on the WooCommerce Payments settings page.
	 */
	public function suppress_admin_notices() {
		global $wp_filter, $current_tab, $current_section;

		$screen = get_current_screen();
		if ( ! $screen instanceof WP_Screen || 'woocommerce_page_wc-settings' !== $screen->id ) {
			return;
		}

		// We only want to suppress notices on the main WooCommerce Payments settings page and Reactified sections.
		if ( self::TAB_NAME !== $current_tab ) {
			return;
		}
		if ( ! $this->should_render_react_section( $current_section ) ) {
			return;
		}

		// Generic admin notices are definitely not needed.
		remove_all_actions( 'all_admin_notices' );

		// WooCommerce uses the 'admin_notices' hook for its own notices.
		// We will only allow WooCommerce core notices to be displayed.
		$wp_admin_notices_hook = $wp_filter['admin_notices'] ?? null;
		if ( ! $wp_admin_notices_hook || ! $wp_admin_notices_hook->has_filters() ) {
			// Nothing to do if there are no actions hooked into `admin_notices`.
			return;
		}

		$wc_admin_notices = WC_Admin_Notices::get_notices();
		if ( empty( $wc_admin_notices ) ) {
			// If there are no WooCommerce core notices, we can remove all actions hooked into `admin_notices`.
			remove_all_actions( 'admin_notices' );
			return;
		}

		// Go through the callbacks hooked into `admin_notices` and
		// remove any that are NOT from the WooCommerce core (i.e. from the `WC_Admin_Notices` class).
		foreach ( $wp_admin_notices_hook->callbacks as $priority => $callbacks ) {
			if ( ! is_array( $callbacks ) ) {
				continue;
			}

			foreach ( $callbacks as $callback ) {
				// Ignore malformed callbacks.
				if ( ! is_array( $callback ) ) {
					continue;
				}
				// WooCommerce doesn't use closures to handle notices.
				// WooCommerce core notices are handled by `WC_Admin_Notices` class methods.
				// Remove plain functions or closures.
				if ( ! is_array( $callback['function'] ) ) {
					remove_action( 'admin_notices', $callback['function'], $priority );
					continue;
				}

				$class_or_object = $callback['function'][0] ?? null;
				// We need to allow Automattic\WooCommerce\Internal\Admin\Loader methods callbacks
				// because they are used to wrap notices.
				// @see Automattic\WooCommerce\Internal\Admin\Loader::inject_before_notices().
				// @see Automattic\WooCommerce\Internal\Admin\Loader::inject_after_notices().
				if (
					(
						// We have a class name.
						is_string( $class_or_object ) &&
						! ( WC_Admin_Notices::class === $class_or_object || Loader::class === $class_or_object )
					) ||
					(
						// We have a class instance.
						is_object( $class_or_object ) &&
						! ( $class_or_object instanceof WC_Admin_Notices || $class_or_object instanceof Loader )
					)
				) {
					remove_action( 'admin_notices', $callback['function'], $priority );
				}
			}
		}
	}

	/**
	 * Suppress the store-alerts WCAdmin feature on the WooCommerce Payments settings page and Reactified sections.
	 *
	 * @param mixed $features The WCAdmin features list.
	 *
	 * @return mixed The modified features list.
	 */
	public function suppress_store_alerts( $features ) {
		global $current_tab, $current_section;

		$feature_name = 'store-alerts';

		if ( is_array( $features ) &&
			in_array( $feature_name, $features, true ) &&
			self::TAB_NAME === $current_tab &&
			$this->should_render_react_section( $current_section ) ) {

			unset( $features[ array_search( $feature_name, $features, true ) ] );
		}

		return $features;
	}
}

return new WC_Settings_Payment_Gateways();
