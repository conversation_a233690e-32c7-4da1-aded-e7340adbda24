"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3534],{3246:(e,o,i)=>{i.d(o,{d:()=>m});var t=i(14908),s=i(36849),n=i(98846),c=i(83306),r=i(27723),a=i(39793);const m=({textProps:e,message:o,eventName:i="",eventProperties:m={},targetUrl:l,linkType:p="wc-admin",target:d,onClickCallback:g})=>{const u=o.match(/{{Link}}(.*?){{\/Link}}/),h=u?u[1]:"",M="external"===p&&"_blank"===d;return(0,a.jsx)(t.Text,{...e,children:(0,s.A)({mixedString:o,components:{Link:(0,a.jsx)(n.Link,{onClick:()=>{if(g?g():(0,c.recordEvent)(i,m),"external"!==p)return window.location.href=l,!1},href:l,type:p,target:M?"_blank":void 0,"aria-label":M?`${h} (${(0,r.__)("opens in a new tab","woocommerce")})`:void 0})}})})}},18886:(e,o,i)=>{function t(e=""){return e?e.split(":")[0]:null}function s(e,o,i=!1,t){return function(e,o=!1,i,t){const s=[];return t?((e.product_types||[]).forEach((e=>{t[e]&&t[e].product&&(o||!i.includes(t[e].slug))&&s.push(t[e])})),s):s}(o,i,t,e).map((e=>e.id||e.product))}i.d(o,{Dr:()=>s,gI:()=>t}),i(18537)},97607:(e,o,i)=>{i.r(o),i.d(o,{default:()=>E});var t=i(47143),s=i(40314),n=i(18886),c=i(27723),r=i(56427),a=i(98846),m=i(96476),l=i(83306),p=i(45155),d=i(3862),g=i(39793);const u=({isPluginInstalled:e})=>{const{layoutString:o}=(0,p.useLayoutContext)();return(0,g.jsxs)("div",{className:"woocommerce-list__item-inner woocommerce-shipping-plugin-item",children:[(0,g.jsx)("div",{className:"woocommerce-list__item-before",children:(0,g.jsx)("img",{className:"woocommerce-shipping-plugin-item__logo",src:d,alt:"WooCommerce Shipping Logo"})}),(0,g.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,g.jsxs)("span",{className:"woocommerce-list__item-title",children:[(0,c.__)("WooCommerce Shipping","woocommerce"),(0,g.jsx)(a.Pill,{children:(0,c.__)("Recommended","woocommerce")})]}),(0,g.jsxs)("span",{className:"woocommerce-list__item-content",children:[(0,c.__)("Print USPS, UPS, and DHL Express labels straight from your WooCommerce dashboard and save on shipping.","woocommerce"),(0,g.jsx)("br",{}),(0,g.jsx)(r.ExternalLink,{href:"https://woocommerce.com/woocommerce-shipping/",children:(0,c.__)("Learn more","woocommerce")})]})]}),(0,g.jsx)("div",{className:"woocommerce-list__item-after",children:(0,g.jsx)(r.Button,{isSecondary:!0,onClick:()=>{(0,l.recordEvent)("tasklist_click",{task_name:"shipping-recommendation",context:`${o}/wc-settings`}),(0,m.navigateTo)({url:(0,m.getNewPath)({task:"shipping-recommendation"},"/",{})})},children:e?(0,c.__)("Activate","woocommerce"):(0,c.__)("Get started","woocommerce")})})]})};var h=i(17336),M=i(86087);const N="woocommerce_admin_reviewed_default_shipping_zones",w="woocommerce_admin_created_default_shipping_zones",x="woocommerce-settings-shipping-tour-floater-wrapper",j="woocommerce-settings-smart-defaults-shipping-tour-floater",_="table.wc-shipping-zones",I='a[href*="woocommerce-shipping-settings"]',D=e=>{const o=e.map((e=>{const o=document?.querySelector(e)?.getBoundingClientRect();if(!o)throw new Error("Shipping tour: Couldn’t find element with selector: "+e);return o})),i=document.querySelector(`.${x}`)?.getBoundingClientRect()||{top:0,left:0},t=Math.min(...o.map((e=>e.top))),s=Math.min(...o.map((e=>e.left))),n=Math.max(...o.map((e=>e.right)))-s,c=Math.max(...o.map((e=>e.bottom)))-t,r=t-i.top;return{left:s-i.left,top:r,width:n,height:c}},y=({dims:e})=>(0,g.jsx)("div",{style:{position:"relative",pointerEvents:"none",...e},className:j}),T=[["th.wc-shipping-zone-sort","tfoot.wc-shipping-zone-worldwide tr > td.wc-shipping-zone-region"],["th.wc-shipping-zone-methods","tfoot.wc-shipping-zone-worldwide tr > td.wc-shipping-zone-methods"]],S=({step:e})=>{var o;const i=(0,M.useRef)(null);(0,M.useLayoutEffect)((()=>{i.current?.parentElement&&i.current.parentElement.insertBefore(i.current,document.querySelector("table.wc-shipping-zones"))}),[]);const t=null!==(o=T[e])&&void 0!==o?o:T[T.length-1],[s,n]=(0,M.useState)(D(t));(0,M.useEffect)((()=>{n(D(t));const e=new ResizeObserver((()=>{n(D(t))})),o=document.querySelector(_);if(!o)throw new Error("Shipping tour: Couldn’t find shipping settings table element with selector: "+_);return e.observe(o),()=>{e.disconnect()}}),[t]);const c=document.querySelector(_)?.parentElement;if(!c)throw new Error("Shipping tour: Couldn’t find shipping settings table parent element with selector: "+_);return(0,M.createPortal)((0,g.jsx)("div",{ref:i,className:x,style:{position:"absolute"},children:(0,g.jsx)(y,{dims:s})}),c)},z=({showShippingRecommendationsStep:e})=>{const{updateOptions:o}=(0,t.useDispatch)(s.optionsStore),{show:i,isUspsDhlEligible:r}=(()=>{const{hasCreatedDefaultShippingZones:e,hasReviewedDefaultShippingOptions:o,businessCountry:i,isLoading:c}=(0,t.useSelect)((e=>{const{hasFinishedResolution:o,getOption:i}=e(s.optionsStore);return{isLoading:!o("getOption",[w])&&!o("getOption",[N])&&!o("getOption",["woocommerce_default_country"]),hasCreatedDefaultShippingZones:"yes"===i(w),hasReviewedDefaultShippingOptions:"yes"===i(N),businessCountry:(0,n.gI)(i("woocommerce_default_country"))}}),[]);return{isLoading:c,show:window.wcAdminFeatures["shipping-setting-tour"]&&!c&&e&&!o,isUspsDhlEligible:"US"===i}})(),[m,p]=(0,M.useState)(0),{createNotice:d}=(0,t.useDispatch)("core/notices"),u={placement:"auto",options:{effects:{spotlight:{interactivity:{enabled:!1}},liveResize:{mutation:!0,resize:!0},autoScroll:!0},callbacks:{onNextStep:e=>{p(e),(0,l.recordEvent)("walkthrough_settings_shipping_next_click",{step_name:u.steps[e-1].meta.name})},onPreviousStep:e=>{p(e),(0,l.recordEvent)("walkthrough_settings_shipping_back_click",{step_name:u.steps[e+1].meta.name})}}},steps:[{referenceElements:{desktop:`.${j}`},meta:{name:"shipping-zones",heading:(0,c.__)("Shipping zones","woocommerce"),descriptions:{desktop:(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{children:(0,c.__)("Specify the areas you’d like to ship to! Give each zone a name, then list the regions you’d like to include. Your regions can be as specific as a zip code or as broad as a country. Shoppers will only see the methods available in their region.","woocommerce")}),(0,g.jsx)("br",{}),(0,g.jsx)("span",{children:(0,c.__)("We’ve added some shipping zones to get you started — you can manage them by selecting Edit or Delete.","woocommerce")})]})}}},{referenceElements:{desktop:`.${j}`},meta:{name:"shipping-methods",heading:(0,c.__)("Shipping methods","woocommerce"),descriptions:{desktop:(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{children:(0,c.__)("Add one or more shipping methods you’d like to offer to shoppers in your zones.","woocommerce")}),(0,g.jsx)("br",{}),(0,g.jsx)("span",{children:(0,c.__)("For example, we’ve added the “Free shipping” method for shoppers in your country. You can edit, add to, or remove shipping methods by selecting Edit or Delete.","woocommerce")})]})}}}],closeHandler:async(e,i,t)=>{(await o({[N]:"yes"})).success||(d("error",(0,c.__)("There was a problem marking the shipping tour as completed.","woocommerce")),(0,l.recordEvent)("walkthrough_settings_shipping_updated_option_error")),"close-btn"===t?(0,l.recordEvent)("walkthrough_settings_shipping_dismissed",{step_name:e[i].meta.name}):"done-btn"===t&&(0,l.recordEvent)("walkthrough_settings_shipping_completed")}};return document.querySelector(I)&&r&&u.steps.push({referenceElements:{desktop:I},meta:{name:"woocommerce-shipping",heading:(0,c.__)("WooCommerce Shipping","woocommerce"),descriptions:{desktop:(0,c.__)("Print USPS, UPS, and DHL labels straight from your Woo dashboard and save on shipping thanks to discounted rates. You can manage WooCommerce Shipping in this section.","woocommerce")}}}),e&&u.steps.push({referenceElements:{desktop:"div.woocommerce-recommended-shipping-extensions"},meta:{name:"shipping-recommendations",heading:(0,c.__)("WooCommerce Shipping","woocommerce"),descriptions:{desktop:(0,c.__)("If you’d like to speed up your process and print your shipping label straight from your Woo dashboard, WooCommerce Shipping may be for you! ","woocommerce")}}}),(0,M.useEffect)((()=>{i&&(0,l.recordEvent)("walkthrough_settings_shipping_view")}),[i]),i?(0,g.jsxs)("div",{children:[(0,g.jsx)(S,{step:m}),(0,g.jsx)(a.TourKit,{config:u})]}):null},E=()=>{const{activePlugins:e,installedPlugins:o,countryCode:i,isSellingDigitalProductsOnly:c}=(0,t.useSelect)((e=>{const o=e(s.settingsStore).getSettings("general"),{getActivePlugins:i,getInstalledPlugins:t}=e(s.pluginsStore),c=e(s.onboardingStore).getProfileItems().product_types;return{activePlugins:i(),installedPlugins:t(),countryCode:(0,n.gI)(o.general?.woocommerce_default_country),isSellingDigitalProductsOnly:1===c?.length&&"downloads"===c[0]}}),[]);return e.includes("woocommerce-shipping")||"US"!==i||c?(0,g.jsx)(z,{showShippingRecommendationsStep:!1}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(z,{showShippingRecommendationsStep:!0}),(0,g.jsx)(h.ShippingRecommendationsList,{children:(0,g.jsx)(u,{isPluginInstalled:o.includes("woocommerce-shipping")})})]})}},17336:(e,o,i)=>{i.r(o),i.d(o,{ShippingRecommendationsList:()=>_,default:()=>I});var t=i(27723),s=i(47143),n=i(86087),c=i(14908),r=i(40314),a=i(15703),m=i(56427),l=i(46772),p=i(98846),d=i(4921),g=i(39793);const u=(0,n.createContext)(""),h=({onDismiss:e=()=>null,children:o})=>{const{updateOptions:i}=(0,s.useDispatch)(r.optionsStore),c=(0,n.useContext)(u),a=()=>{e(),i({[c]:"yes"})};return(0,g.jsxs)(m.CardHeader,{children:[(0,g.jsx)("div",{className:"woocommerce-dismissable-list__header",children:o}),(0,g.jsx)("div",{children:(0,g.jsx)(p.EllipsisMenu,{label:(0,t.__)("Task List Options","woocommerce"),renderContent:()=>(0,g.jsx)("div",{className:"woocommerce-dismissable-list__controls",children:(0,g.jsx)(m.Button,{onClick:a,children:(0,t.__)("Hide this","woocommerce")})})})})]})},M=({children:e,className:o,dismissOptionName:i})=>(0,s.useSelect)((e=>{const{getOption:o,hasFinishedResolution:t}=e(r.optionsStore),s=t("getOption",[i]),n="yes"===o(i);return s&&!n}),[i])?(0,g.jsx)(m.Card,{size:"medium",className:(0,d.A)("woocommerce-dismissable-list",o),children:(0,g.jsx)(u.Provider,{value:i,children:e})}):null;var N=i(3862);const w=({onSetupClick:e,pluginsBeingSetup:o})=>{const{createSuccessNotice:i}=(0,s.useDispatch)("core/notices"),n=(0,s.useSelect)((e=>e(r.pluginsStore).isJetpackConnected()),[]);return(0,g.jsxs)("div",{className:"woocommerce-list__item-inner woocommerce-shipping-plugin-item",children:[(0,g.jsx)("div",{className:"woocommerce-list__item-before",children:(0,g.jsx)("img",{className:"woocommerce-shipping-plugin-item__logo",src:N,alt:""})}),(0,g.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,g.jsxs)("span",{className:"woocommerce-list__item-title",children:[(0,t.__)("WooCommerce Shipping","woocommerce"),(0,g.jsx)(p.Pill,{children:(0,t.__)("Recommended","woocommerce")})]}),(0,g.jsxs)("span",{className:"woocommerce-list__item-content",children:[(0,t.__)("Print USPS, UPS, and DHL Express labels straight from your WooCommerce dashboard and save on shipping.","woocommerce"),(0,g.jsx)("br",{}),(0,g.jsx)(m.ExternalLink,{href:"https://woocommerce.com/woocommerce-shipping/",children:(0,t.__)("Learn more","woocommerce")})]})]}),(0,g.jsx)("div",{className:"woocommerce-list__item-after",children:(0,g.jsx)(m.Button,{isSecondary:!0,onClick:()=>{e(["woocommerce-shipping"]).then((()=>{const e=[];n||e.push({url:(0,a.getAdminLink)("admin.php?page=wc-settings&tab=shipping&section=woocommerce-shipping-settings"),label:(0,t.__)("Finish the setup by connecting your store to WordPress.com.","woocommerce")}),i((0,t.__)("🎉 WooCommerce Shipping is installed!","woocommerce"),{actions:e})}))},isBusy:o.includes("woocommerce-shipping"),disabled:o.length>0,children:(0,t.__)("Get started","woocommerce")})})]})};var x=i(3246),j=i(51684);const _=({children:e})=>(0,g.jsxs)(M,{className:"woocommerce-recommended-shipping-extensions",dismissOptionName:"woocommerce_settings_shipping_recommendations_hidden",children:[(0,g.jsxs)(h,{children:[(0,g.jsx)(c.Text,{variant:"title.small",as:"p",size:"20",lineHeight:"28px",children:(0,t.__)("Recommended shipping solutions","woocommerce")}),(0,g.jsx)(c.Text,{className:"woocommerce-recommended-shipping__header-heading",variant:"caption",as:"p",size:"12",lineHeight:"16px",children:(0,t.__)('We recommend adding one of the following shipping extensions to your store. The extension will be installed and activated for you when you click "Get started".',"woocommerce")})]}),(0,g.jsx)("ul",{className:"woocommerce-list",children:n.Children.map(e,(e=>(0,g.jsx)("li",{className:"woocommerce-list__item",children:e})))}),(0,g.jsx)(m.CardFooter,{children:(0,g.jsx)(x.d,{message:(0,t.__)("Visit {{Link}}the WooCommerce Marketplace{{/Link}} to find more shipping, delivery, and fulfillment solutions.","woocommerce"),targetUrl:(0,j.isFeatureEnabled)("marketplace")?(0,a.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=shipping-delivery-and-fulfillment"):"https://woocommerce.com/product-category/woocommerce-extensions/shipping-delivery-and-fulfillment/",linkType:(0,j.isFeatureEnabled)("marketplace")?"wc-admin":"external",eventName:"settings_shipping_recommendation_visit_marketplace_click"})})]}),I=()=>{const[e,o]=(()=>{const[e,o]=(0,n.useState)([]),{installAndActivatePlugins:i}=(0,s.useDispatch)(r.pluginsStore);return[e,t=>e.length>0?Promise.resolve():(o(t),i(t).then((()=>{o([])})).catch((e=>((0,l.R)(e),o([]),Promise.reject()))))]})();return(0,s.useSelect)((e=>e(r.pluginsStore).getActivePlugins()),[]).includes("woocommerce-shipping")?null:(0,g.jsx)(_,{children:(0,g.jsx)(w,{pluginsBeingSetup:e,onSetupClick:o})})}},51684:(e,o,i)=>{i.d(o,{isFeatureEnabled:()=>n});var t=i(56109);const s="features";function n(e){const o=function(e){const o=(0,t.Qk)(s);return o&&o[e]}(e);return Boolean(o?.is_enabled)}},3862:e=>{e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIKICAgICB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogICAgPHJlY3Qgd2lkdGg9IjI4IiBoZWlnaHQ9IjI4IiByeD0iNS4yNSIgZmlsbD0iIzg3M0VGRiIvPgogICAgPHBhdGggZD0iTTYuMTgyMTcgMTYuNzIzNkM2Ljc5NTg5IDE2LjcyMzYgNy4yODgzOCAxNi40MjA1IDcuNjU5NjQgMTUuNzIzNEw4LjQ4NTUgMTQuMTc3OFYxNS40ODg2QzguNDg1NSAxNi4yNjE0IDguOTg1NTcgMTYuNzIzNiA5Ljc1ODQgMTYuNzIzNkMxMC4zNjQ1IDE2LjcyMzYgMTAuODExNiAxNi40NTg0IDExLjI0MzQgMTUuNzIzNEwxMy4xNDUyIDEyLjUxMDlDMTMuNTYxOSAxMS44MDYzIDEzLjI2NjQgMTEuMjc1OSAxMi4zNDk2IDExLjI3NTlDMTEuODU3MiAxMS4yNzU5IDExLjUzODkgMTEuNDM1IDExLjI1MSAxMS45NzI5TDkuOTQwMjQgMTQuNDM1NFYxMi4yNDU3QzkuOTQwMjQgMTEuNTk0MSA5LjYyOTU5IDExLjI3NTkgOS4wNTM3NiAxMS4yNzU5QzguNTk5MTUgMTEuMjc1OSA4LjIzNTQ3IDExLjQ3MjkgNy45NTUxMyAxMi4wMTg0TDYuNzIwMTIgMTQuNDM1NFYxMi4yNjg0QzYuNzIwMTIgMTEuNTcxNCA2LjQzMjIgMTEuMjc1OSA1LjczNTE0IDExLjI3NTlINC4zMTA3MUMzLjc3Mjc2IDExLjI3NTkgMy41IDExLjUyNTkgMy41IDExLjk4ODFDMy41IDEyLjQ1MDMgMy43ODc5MiAxMi43MTU1IDQuMzEwNzEgMTIuNzE1NUg0Ljg5NDEyVjE1LjQ4MUM0Ljg5NDEyIDE2LjI2MTQgNS40MTY5MiAxNi43MjM2IDYuMTgyMTcgMTYuNzIzNloiIGZpbGw9IndoaXRlIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE1LjkyNjMgMTEuMjc1OUMxNC4zNzMgMTEuMjc1OSAxMy4xODM1IDEyLjQzNTEgMTMuMTgzNSAxNC4wMDM1QzEzLjE4MzUgMTUuNTcxOSAxNC4zODA2IDE2LjcyMzYgMTUuOTI2MyAxNi43MjM2QzE3LjQ3MTkgMTYuNzIzNiAxOC42NTM5IDE1LjU2NDMgMTguNjYxNSAxNC4wMDM1QzE4LjY2MTUgMTIuNDM1MSAxNy40NzE5IDExLjI3NTkgMTUuOTI2MyAxMS4yNzU5Wk0xNS45MjYzIDE1LjA0OTFDMTUuMzQyOSAxNS4wNDkxIDE0Ljk0MTMgMTQuNjA5NyAxNC45NDEzIDE0LjAwMzVDMTQuOTQxMyAxMy4zOTc0IDE1LjM0MjkgMTIuOTUwMyAxNS45MjYzIDEyLjk1MDNDMTYuNTA5NyAxMi45NTAzIDE2LjkxMTMgMTMuMzk3NCAxNi45MTEzIDE0LjAwMzVDMTYuOTExMyAxNC42MDk3IDE2LjUxNzMgMTUuMDQ5MSAxNS45MjYzIDE1LjA0OTFaIiBmaWxsPSJ3aGl0ZSIvPgogICAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xOS4wNDc0IDE0LjAwMzVDMTkuMDQ3NCAxMi40MzUxIDIwLjIzNjkgMTEuMjc1OSAyMS43ODI2IDExLjI3NTlDMjMuMzI4MiAxMS4yNzU5IDI0LjUxNzggMTIuNDQyNyAyNC41MTc4IDE0LjAwMzVDMjQuNTE3OCAxNS41NjQzIDIzLjMyODIgMTYuNzIzNiAyMS43ODI2IDE2LjcyMzZDMjAuMjM2OSAxNi43MjM2IDE5LjA0NzQgMTUuNTcxOSAxOS4wNDc0IDE0LjAwMzVaTTIwLjgwNTIgMTQuMDAzNUMyMC44MDUyIDE0LjYwOTcgMjEuMTkxNiAxNS4wNDkxIDIxLjc4MjYgMTUuMDQ5MUMyMi4zNjYgMTUuMDQ5MSAyMi43Njc1IDE0LjYwOTcgMjIuNzY3NSAxNC4wMDM1QzIyLjc2NzUgMTMuMzk3NCAyMi4zNjYgMTIuOTUwMyAyMS43ODI2IDEyLjk1MDNDMjEuMTk5MiAxMi45NTAzIDIwLjgwNTIgMTMuMzk3NCAyMC44MDUyIDE0LjAwMzVaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K"}}]);