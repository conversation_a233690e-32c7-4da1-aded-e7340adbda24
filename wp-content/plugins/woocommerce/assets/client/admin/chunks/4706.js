"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4706],{74706:(e,t,a)=>{a.r(t),a.d(t,{ProductsApp:()=>Fe});var i=a(86087),s=a(71628),n=a(43656),r=a(35434);const{lock:l,unlock:o}=(0,r.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site");var c=a(97961),m=a(40314),u=a(51157),d=a(25434),p=a(43112),_=a(3582),g=a(27723),v=a(47143),f=a(4921),b=a(56427),h=a(77892),y=a(29706),E=a(98639),w=a(82022),N=a(27200),k=a(22150);const A="grid",x="table",S="list",T="is",P={[x]:{layout:{primaryField:"name",styles:{name:{maxWidth:300}}}},[A]:{layout:{mediaField:"featured-image",primaryField:"name"}},[S]:{layout:{primaryField:"name",mediaField:"featured-image"}}},C={type:x,search:"",filters:[],page:1,perPage:20,sort:{field:"date",direction:"desc"},fields:["name","sku","status","date"],layout:P[S].layout};function L({postType:e}){const t=(0,v.useSelect)((t=>{const{getPostType:a}=t(_.store),i=a(e);return i?.labels}),[e]);return(0,i.useMemo)((()=>[{title:t?.all_items||(0,g.__)("All items","woocommerce"),slug:"all",icon:h.A,view:{...C}},{title:(0,g.__)("Published","woocommerce"),slug:"published",icon:y.A,view:{...C,filters:[{field:"status",operator:T,value:"publish"}]}},{title:(0,g.__)("Scheduled","woocommerce"),slug:"future",icon:E.A,view:{...C,filters:[{field:"status",operator:T,value:"future"}]}},{title:(0,g.__)("Drafts","woocommerce"),slug:"drafts",icon:w.A,view:{...C,filters:[{field:"status",operator:T,value:"draft"}]}},{title:(0,g.__)("Private","woocommerce"),slug:"private",icon:N.A,view:{...C,filters:[{field:"status",operator:T,value:"private"}]}},{title:(0,g.__)("Trash","woocommerce"),slug:"trash",icon:k.A,view:{...C,type:x,layout:P[x].layout,filters:[{field:"status",operator:T,value:"trash"}]}}]),[t])}const I=[{value:"draft",label:(0,g.__)("Draft","woocommerce")},{value:"future",label:(0,g.__)("Scheduled","woocommerce")},{value:"private",label:(0,g.__)("Private","woocommerce")},{value:"publish",label:(0,g.__)("Published","woocommerce")},{value:"trash",label:(0,g.__)("Trash","woocommerce")}],F=[{id:"name",label:(0,g.__)("Name","woocommerce"),enableHiding:!1,type:"text",render:function({item:e}){return(0,i.createElement)(i.Fragment,null,e.name)}},{id:"sku",label:(0,g.__)("SKU","woocommerce"),enableHiding:!1,enableSorting:!1,render:({item:e})=>(0,i.createElement)(i.Fragment,null,e.sku)},{id:"date",label:(0,g.__)("Date","woocommerce"),render:({item:e})=>(0,i.createElement)("time",null,e.date_created)},{label:(0,g.__)("Status","woocommerce"),id:"status",getValue:({item:e})=>{var t;return null!==(t=I.find((({value:t})=>t===e.status))?.label)&&void 0!==t?t:e.status},elements:I,filterBy:{operators:[T]},enableSorting:!1}];var R=a(62978);const{useHistory:H,useLocation:D}=o(s.privateApis),B=({postType:e})=>{const t=H(),a=D();return(0,i.useMemo)((()=>({id:"edit-product",label:(0,g.__)("Edit","woocommerce"),isPrimary:!0,icon:R.A,supportsBulk:!0,isEligible:e=>"trash"!==e.status,callback(i){const s=i[0];t.push({...a.params,postId:s.id,postType:e,quickEdit:!0})}})),[t,a.params])},U=(0,i.createContext)(null);function V({children:e}){const[t,a]=(0,i.useState)(!1);return(0,i.createElement)(U.Provider,{value:{showNewNavigation:t,setShowNewNavigation:a}},e)}function M(){const e=(0,i.useContext)(U);if(e){const{showNewNavigation:t,setShowNewNavigation:a}=e;return[t,a]}return[!1,()=>{}]}const{NavigableRegion:q,usePostActions:z}=o(n.privateApis),{useHistory:j,useLocation:W}=o(s.privateApis),O=25,G=[],K=(e,t)=>e.find((({slug:e})=>e===t))?.view;function Q(e){return e.id.toString()}function J({subTitle:e,className:t,hideTitleFromUI:a=!1}){const[s,n]=M(),r=j(),l=W(),{postId:o,quickEdit:h=!1,postType:y="product",isCustom:E,activeView:w="all"}=l.params,[N,k]=(0,i.useState)([o]),[A,x]=function(e){const{params:{activeView:t="all",isCustom:a="false",layout:s}}=W(),n=j(),r=L({postType:e}),[l,o]=(0,i.useState)((()=>{var e;const a=null!==(e=K(r,t))&&void 0!==e?e:{type:null!=s?s:S},i=null!=s?s:a.type;return{...a,type:i}})),c=(0,i.useCallback)((e=>{const{params:t}=n.getLocationWithParams();(e.type!==S||t?.layout)&&e.type!==t?.layout&&n.push({...t,layout:e.type}),o(e)}),[n]);return(0,i.useEffect)((()=>{o((e=>({...e,type:null!=s?s:S})))}),[s]),(0,i.useEffect)((()=>{const e=K(r,t);if(e){const t=null!=s?s:e.type;o({...e,type:t})}}),[t,a,s,r]),[l,c,c]}(y),T=(0,i.useMemo)((()=>{const e={};A.filters?.forEach((t=>{"status"===t.field&&(e.status=Array.isArray(t.value)?t.value.join(","):t.value)}));const t="name"===A.sort?.field?"title":A.sort?.field;return{per_page:A.perPage,page:A.page,order:A.sort?.direction,orderby:t,search:A.search,...e}}),[A]),C=(0,i.useCallback)((e=>{k(e),r.push({...l.params,postId:e.join(",")})}),[r,l.params]),{records:I,totalCount:R,isLoading:H}=(0,v.useSelect)((e=>{const{getProducts:t,getProductsTotalCount:a,isResolving:i}=e(m.productsStore);return{records:t(T),totalCount:a(T),isLoading:i("getProducts",[T])}}),[T]),D=(0,i.useMemo)((()=>({totalItems:null!=R?R:0,totalPages:Math.ceil((null!=R?R:0)/(A.perPage||O))})),[R,A.perPage]),{labels:U,canCreateRecord:V}=(0,v.useSelect)((e=>{const{getPostType:t,canUser:a}=e(_.store),i=t(y);return{labels:i?.labels,canCreateRecord:a("create",{kind:"postType",name:y})}}),[y]),J=z({postType:y,context:"list"}),X=B({postType:y}),Y=(0,i.useMemo)((()=>[X,...J]),[J,X]),Z=(0,f.A)("edit-site-page",t);return(0,i.createElement)(q,{className:Z,ariaLabel:(0,g.__)("Products","woocommerce")},(0,i.createElement)("div",{className:"edit-site-page-content"},!a&&(0,i.createElement)(b.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(b.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(b.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,g.__)("Products","woocommerce")),(0,i.createElement)(b.FlexItem,{className:"edit-site-page-header__actions"},U?.add_new_item&&V&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)(b.Button,{variant:"primary",disabled:!0,__next40pxDefaultSize:!0},U.add_new_item)))),e&&(0,i.createElement)(b.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),(0,i.createElement)(c.A,{key:w+E,paginationInfo:D,fields:F,data:I||G,isLoading:H,view:A,actions:Y,onChangeView:x,onChangeSelection:C,getItemId:Q,selection:N,defaultLayouts:P,header:(0,i.createElement)(i.Fragment,null,(0,i.createElement)(b.Button,{size:"compact",icon:s?u.A:d.A,label:(0,g.__)("Toggle navigation","woocommerce"),onClick:()=>{n(!s)}}),(0,i.createElement)(b.Button,{size:"compact",isPressed:h,icon:p.A,label:(0,g.__)("Toggle details panel","woocommerce"),onClick:()=>{r.push({...l.params,quickEdit:!h||void 0})}}))})))}var X=a(75288),Y=a(92428);const{NavigableRegion:Z}=o(n.privateApis),$={type:"panel",fields:["name","status"]};function ee({subTitle:e,actions:t,className:a,hideTitleFromUI:s=!0,postType:n,postId:r=""}){const l=(0,f.A)("edit-product-page",a,{"is-empty":!r}),o=(0,i.useMemo)((()=>r.split(",")),[r]),{initialEdits:c}=(0,v.useSelect)((e=>({initialEdits:1===o.length?e(m.productsStore).getProduct(Number.parseInt(o[0],10)):null})),[n,o]),[u,d]=(0,i.useState)({}),p=(0,i.useMemo)((()=>({...c,...u})),[c,u]),_=!(0,X.q)(p,F,$);return(0,i.createElement)(Z,{className:l,ariaLabel:(0,g.__)("Product Edit","woocommerce")},(0,i.createElement)("div",{className:"edit-product-content"},!s&&(0,i.createElement)(b.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(b.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(b.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,g.__)("Product Edit","woocommerce")),(0,i.createElement)(b.FlexItem,{className:"edit-site-page-header__actions"},t)),e&&(0,i.createElement)(b.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),!r&&(0,i.createElement)("p",null,(0,g.__)("Select a product to edit","woocommerce")),r&&(0,i.createElement)(b.__experimentalVStack,{spacing:4,as:"form",onSubmit:async e=>{e.preventDefault(),(0,X.q)(p,F,$)&&d({})}},(0,i.createElement)(Y.A,{data:p,fields:F,form:$,onChange:d}),(0,i.createElement)(b.FlexItem,null,(0,i.createElement)(b.Button,{variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:_,__next40pxDefaultSize:!0},(0,g.__)("Update","woocommerce"))))))}var te=a(93832),ae=a(45197),ie=a(5520),se=a(29543),ne=a(40738);const{useHistory:re}=o(s.privateApis);function le({className:e,icon:t,withChevron:a=!1,suffix:s,uid:n,params:r,onClick:l,children:o,...c}){const m=re();return(0,i.createElement)(b.__experimentalItem,{className:(0,f.A)("edit-site-sidebar-navigation-item",{"with-suffix":!a&&s},e),onClick:function(e){l?l(e):r&&(e.preventDefault(),m.push(r))},id:n,...c},(0,i.createElement)(b.__experimentalHStack,{justify:"flex-start"},t&&(0,i.createElement)(ie.A,{style:{fill:"currentcolor"},icon:t,size:24}),(0,i.createElement)(b.FlexBlock,null,o),a&&(0,i.createElement)(ie.A,{icon:(0,g.isRTL)()?se.A:ne.A,className:"edit-site-sidebar-navigation-item__drilldown-indicator",size:24}),!a&&s))}const{useHistory:oe,useLocation:ce}=o(s.privateApis);function me({title:e,slug:t,customViewId:a,type:s,icon:n,isActive:r,isCustom:l,suffix:o}){const{params:{postType:c,page:m}}=ce(),u=n||ae.Ad.find((e=>e.type===s))?.icon;let d=l?a:t;"all"===d&&(d=void 0);const p=function(e,t,a=!1){const i=oe(),s=(0,te.getQueryArgs)(window.location.href),n=(0,te.removeQueryArgs)(window.location.href,...Object.keys(s));return{href:(0,te.addQueryArgs)(n,e),onClick:function(s){s?.preventDefault(),a?i.replace(e,t):i.push(e,t)}}}({page:m,postType:c,layout:s,activeView:d,isCustom:l?"true":void 0});return(0,i.createElement)(b.__experimentalHStack,{justify:"flex-start",className:(0,f.A)("edit-site-sidebar-dataviews-dataview-item",{"is-selected":r})},(0,i.createElement)(le,{icon:u,...p,"aria-current":r?"true":void 0},e),o)}const{useLocation:ue}=o(s.privateApis);function de(){const{params:{postType:e="product",activeView:t="all",isCustom:a="false"}}=ue(),s=L({postType:e});if(!e)return null;const n="true"===a;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(b.__experimentalItemGroup,null,s.map((e=>(0,i.createElement)(me,{key:e.slug,slug:e.slug,title:e.title,icon:e.icon,type:e.view.type,isActive:!n&&e.slug===t,isCustom:!1})))))}var pe=a(31496),_e=a(89505);function ge(e){return(0,i.createElement)(b.Button,{...e,className:(0,f.A)("edit-site-sidebar-button",e.className)})}const{useHistory:ve,useLocation:fe}=o(s.privateApis);function be({isRoot:e,title:t,actions:a,meta:s,content:n,footer:r,description:l,backPath:c}){const{dashboardLink:m,dashboardLinkText:u}=(0,v.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site"));return{dashboardLink:t().__experimentalDashboardLink,dashboardLinkText:t().__experimentalDashboardLinkText}}),[]),d=fe(),p=ve(),_=null!=c?c:d.state?.backPath,h=(0,g.isRTL)()?pe.A:_e.A;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(b.__experimentalVStack,{className:(0,f.A)("edit-site-sidebar-navigation-screen__main",{"has-footer":!!r}),spacing:0,justify:"flex-start"},(0,i.createElement)(b.__experimentalHStack,{spacing:3,alignment:"flex-start",className:"edit-site-sidebar-navigation-screen__title-icon"},!e&&(0,i.createElement)(ge,{onClick:()=>{p.push(_)},icon:h,label:(0,g.__)("Back","woocommerce"),showTooltip:!1}),e&&(0,i.createElement)(ge,{icon:h,label:u||(0,g.__)("Go to the Dashboard","woocommerce"),href:m||"index.php"}),(0,i.createElement)(b.__experimentalHeading,{as:"h1",className:"edit-site-sidebar-navigation-screen__title",color:"#e0e0e0",level:1},t),a&&(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__actions"},a)),s&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__meta"},s)),(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__content"},l&&(0,i.createElement)("p",{className:"edit-site-sidebar-navigation-screen__description"},l),n)),r&&(0,i.createElement)("footer",{className:"edit-site-sidebar-navigation-screen__footer"},r))}const{useLocation:he}=o(s.privateApis);var ye=a(29491);function Ee({children:e}){const t=(0,i.useRef)(null);return(0,i.createElement)("div",{ref:t,className:"edit-site-sidebar__screen-wrapper"},e)}function we({routeKey:e,children:t}){return(0,i.createElement)("div",{className:"edit-site-sidebar__content"},(0,i.createElement)(Ee,{key:e},t))}var Ne=a(18537),ke=a(30267);const Ae=function({className:e}){const{isRequestingSite:t,siteIconUrl:a}=(0,v.useSelect)((e=>{const{getEntityRecord:t}=e(_.store),a=t("root","__unstableBase");return{isRequestingSite:!a,siteIconUrl:a?.site_icon_url}}),[]);if(t&&!a)return(0,i.createElement)("div",{className:"edit-site-site-icon__image"});const s=a?(0,i.createElement)("img",{className:"edit-site-site-icon__image",alt:(0,g.__)("Site Icon","woocommerce"),src:a}):(0,i.createElement)(b.Icon,{className:"edit-site-site-icon__icon",icon:ke.A,size:48});return(0,i.createElement)("div",{className:(0,f.A)(e,"edit-site-site-icon")},s)},xe=(0,i.memo)((0,i.forwardRef)((({isTransparent:e},t)=>{const{dashboardLink:a,homeUrl:s,siteTitle:n}=(0,v.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site")),{getSite:a,getUnstableBase:i}=e(_.store),s=a(),n=i();return{dashboardLink:t().__experimentalDashboardLink||"index.php",homeUrl:n?.home,siteTitle:!s?.title&&s?.url?(0,te.filterURLForDisplay)(s?.url):s?.title}}),[]);return(0,i.createElement)("div",{className:"edit-site-site-hub"},(0,i.createElement)(b.__experimentalHStack,{justify:"flex-start",spacing:"0"},(0,i.createElement)("div",{className:(0,f.A)("edit-site-site-hub__view-mode-toggle-container",{"has-transparent-background":e})},(0,i.createElement)(b.Button,{ref:t,href:a,label:(0,g.__)("Go to the Dashboard","woocommerce"),className:"edit-site-layout__view-mode-toggle",style:{transform:"scale(0.5)",borderRadius:4}},(0,i.createElement)(Ae,{className:"edit-site-layout__view-mode-toggle-icon"}))),(0,i.createElement)(b.__experimentalHStack,null,(0,i.createElement)("div",{className:"edit-site-site-hub__title"},(0,i.createElement)(b.Button,{variant:"link",href:s,target:"_blank"},n&&(0,Ne.decodeEntities)(n),(0,i.createElement)(b.VisuallyHidden,{as:"span"},(0,g.__)("(opens in a new tab)","woocommerce")))))))}))),{NavigableRegion:Se}=o(n.privateApis),Te=.3;function Pe({route:e,showNewNavigation:t=!1}){const[a]=(0,ye.useResizeObserver)(),s=(0,i.useRef)(null),r=(0,ye.useViewportMatch)("medium","<"),l=(0,ye.useReducedMotion)(),{key:o,areas:c,widths:m}=e;return(0,i.createElement)(i.Fragment,null,a,(0,i.createElement)("div",{className:"edit-site-layout"},(0,i.createElement)("div",{className:"edit-site-layout__content"},(!r||!c.mobile)&&t&&(0,i.createElement)(Se,{ariaLabel:(0,g.__)("Navigation","woocommerce"),className:"edit-site-layout__sidebar-region"},(0,i.createElement)(b.__unstableAnimatePresence,null,(0,i.createElement)(b.__unstableMotion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{type:"tween",duration:l||r?0:Te,ease:"easeOut"},className:"edit-site-layout__sidebar"},(0,i.createElement)(xe,{ref:s,isTransparent:!1}),(0,i.createElement)(we,{routeKey:o},c.sidebar)))),(0,i.createElement)(n.EditorSnackbars,null),!r&&c.content&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:m?.content}},c.content),!r&&c.edit&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:m?.edit}},c.edit))))}const{RouterProvider:Ce}=o(s.privateApis),{GlobalStylesProvider:Le}=o(n.privateApis);function Ie(){const[e]=M();e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode");const t=function(){const{params:e={}}=he(),{postType:t="product",layout:a="table",canvas:s,quickEdit:n,postId:r}=e;if(["product"].includes(t)){const e="list"===a||!a;return{key:"products-list",areas:{sidebar:(0,i.createElement)(be,{title:"Products",isRoot:!0,content:(0,i.createElement)(de,null)}),content:(0,i.createElement)(J,null),preview:!1,mobile:(0,i.createElement)(J,{postType:t}),edit:n&&(0,i.createElement)(ee,{postType:t,postId:r})},widths:{edit:n&&!e?380:void 0}}}return{key:"default",areas:{preview:!1,mobile:"edit"===s}}}();return(0,i.createElement)(Pe,{route:t,showNewNavigation:e})}function Fe(){return(0,i.createElement)(V,null,(0,i.createElement)(Le,null,(0,i.createElement)(n.UnsavedChangesWarning,null),(0,i.createElement)(Ce,null,(0,i.createElement)(Ie,null))))}}}]);