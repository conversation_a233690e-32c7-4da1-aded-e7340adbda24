"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[7210],{12974:(e,t,n)=>{n.d(t,{Ay:()=>s});var o=n(13240);const i=["a","b","em","i","strong","p","br"],a=["target","href","rel","name","download"],s=e=>({__html:(0,o.sanitize)(e,{ALLOWED_TAGS:i,ALLOWED_ATTR:a})})},75753:(e,t,n)=>{n.d(t,{LO:()=>w,PE:()=>p,S:()=>_,CS:()=>h}),n(18982);var o=n(27723),i=n(56427),a=n(86087),s=n(47143),r=n(40314),c=n(96476),l=n(1069),d=n(22861),m=n(39793);const _=({gatewayProvider:e,settingsHref:t,onboardingHref:n,isOffline:_,acceptIncentive:g=()=>{},gatewayHasRecommendedPaymentMethods:u,installingPlugin:w,buttonText:p=(0,o.__)("Enable","woocommerce"),incentive:y=null,setOnboardingModalOpen:f,onboardingType:h})=>{const[b,v]=(0,a.useState)(!1),{createErrorNotice:x}=(0,s.dispatch)("core/notices"),{togglePaymentGateway:P,invalidateResolutionForStoreSelector:S}=(0,s.useDispatch)(r.paymentSettingsStore),j=()=>{x((0,o.__)("An error occurred. You will be redirected to the settings page, try enabling the payment gateway there.","woocommerce"),{type:"snackbar",explicitDismiss:!0})};return(0,m.jsx)(i.Button,{variant:"primary",isBusy:b,disabled:b||!!w,onClick:i=>{if(i.preventDefault(),e.state.enabled)return;(0,l.g2)("enable_click",e,{incentive_id:y?y.promo_id:"none"});const a=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!a)return j(),void(window.location.href=t);v(!0),y&&g(y.promo_id),P(e.id,window.woocommerce_admin.ajax_url,a).then((i=>{if("needs_setup"===i.data)if(e.state.account_connected)x((0,o.__)("The provider could not be enabled. Check the Manage page for details.","woocommerce"),{type:"snackbar",explicitDismiss:!0,actions:[{label:(0,o.__)("Manage","woocommerce"),url:t}]}),(0,l.g2)("enable_failed",e,{reason:"needs_setup",incentive_id:y?y.promo_id:"none"});else if("native_in_context"===h&&f)(0,l.W7)("woopayments_onboarding_modal_opened",{from:"enable_gateway_button",source:d.Fx}),f(!0);else{if(!u)return void(window.location.href=n);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}S("getPaymentProviders"),_&&S("getOfflinePaymentGateways"),v(!1)})).catch((()=>{(0,l.g2)("enable_failed",e,{reason:"error",incentive_id:y?y.promo_id:"none"}),v(!1),j(),window.location.href=t}))},href:t,children:p})};var g=n(1455),u=n.n(g);const w=({acceptIncentive:e,installingPlugin:t,buttonText:n=(0,o.__)("Activate payments","woocommerce"),incentive:s=null,setOnboardingModalOpen:r,onboardingType:c,disableTestAccountUrl:_})=>{const[g,w]=(0,a.useState)(!1);return(0,m.jsx)(i.Button,{variant:"primary",isBusy:g,disabled:g||!!t,onClick:()=>{if(w(!0),(0,l.TH)("activate_payments_button_click",{provider_id:d.$8,suggestion_id:d.eD,incentive_id:s?s.promo_id:"none",onboarding_type:c||"unknown",provider_extension_slug:d.bw}),!_)return s&&e(s.promo_id),w(!1),void("native_in_context"===c?((0,l.W7)("woopayments_onboarding_modal_opened",{from:"activate_payments_button",source:d.Fx}),r(!0)):window.location.href=(0,l.ZV)());u()({url:_,method:"POST"}).then((()=>{s&&e(s.promo_id),w(!1),"native_in_context"===c?((0,l.W7)("woopayments_onboarding_modal_opened",{from:"activate_payments_button",source:d.Fx}),r(!0)):window.location.href=(0,l.ZV)()})).catch((()=>{w(!1)}))},children:n})},p=({gatewayProvider:e,settingsHref:t,onboardingHref:n,gatewayHasRecommendedPaymentMethods:_,installingPlugin:g,buttonText:u=(0,o.__)("Complete setup","woocommerce"),setOnboardingModalOpen:w,onboardingType:p,acceptIncentive:y=()=>{},incentive:f=null})=>{const[h,b]=(0,a.useState)(!1),{select:v}=(0,s.useSelect)((e=>({select:e})),[]),x=e.state.account_connected,P=e.onboarding.state.started,S=e.onboarding.state.completed;return(0,a.useEffect)((()=>{(0,l.j4)(e.id)&&"native_in_context"===p&&!S&&v(r.woopaymentsOnboardingStore).getOnboardingData()}),[e.id,S,p,v]),(0,m.jsx)(i.Button,{variant:"primary",isBusy:h,disabled:h||!!g,onClick:()=>{if((0,l.g2)("complete_setup_click",e),b(!0),f&&y(f.promo_id),"native_in_context"===p)(0,l.W7)("woopayments_onboarding_modal_opened",{from:"complete_setup_button",source:d.Fx}),w(!0);else{if(x&&P)return x&&P&&!S?void(window.location.href=n):void(window.location.href=t);if(!_)return void(window.location.href=n);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}b(!1)},children:u},e.id)};var y=n(33068),f=n(93832);const h=({gatewayProvider:e,settingsHref:t,isInstallingPlugin:n,buttonText:a=(0,o.__)("Manage","woocommerce")})=>{const c=!!(0,f.getQueryArg)(t,"path"),d=(0,y.Zp)(),{invalidateResolutionForStoreSelector:_}=(0,s.useDispatch)(r.paymentGatewaysStore);return(0,m.jsx)(i.Button,{variant:"secondary",disabled:n,onClick:n=>{(0,l.g2)("provider_manage_click",e),n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||1===n.button?window.open(t,"_blank"):c?(_("getPaymentGateway"),d((0,l.Wg)(t))):window.location.href=t},children:a})}},69222:(e,t,n)=>{n.r(t),n.d(t,{SettingsPaymentsOffline:()=>u,default:()=>w});var o=n(47143),i=n(86087),a=n(40314),s=n(51881),r=n(18537),c=n(12974),l=n(15698),d=n(75753),m=n(39793);const _=({gateway:e,...t})=>(0,m.jsx)(l.Uq,{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done"+(t.className?` ${t.className}`:""),...t,children:(0,m.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,m.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,m.jsx)(l.Gh,{}),e.icon&&(0,m.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,m.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,m.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,m.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,c.Ay)((0,r.decodeEntities)(e.description))})]}),(0,m.jsx)("div",{className:"woocommerce-list__item-after",children:(0,m.jsx)("div",{className:"woocommerce-list__item-after__actions",children:e.state.enabled?(0,m.jsx)(d.CS,{gatewayProvider:e,settingsHref:e.management._links.settings.href,isInstallingPlugin:!1}):(0,m.jsx)(d.S,{installingPlugin:null,gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!0,gatewayHasRecommendedPaymentMethods:!1})})})]})},e.id),g=({gateways:e,setGateways:t})=>(0,m.jsx)(l.q6,{className:"woocommerce-list",items:e,setItems:t,children:e.map(((t,n)=>(0,m.jsx)(_,{gateway:t,className:"woocommerce-list__item"+(n===e.length-1?" is-last":"")},t.id)))}),u=()=>{const{offlinePaymentGateways:e,isFetching:t}=(0,o.useSelect)((e=>{const t=e(a.paymentSettingsStore);return{offlinePaymentGateways:t.getOfflinePaymentGateways(),isFetching:t.isFetching()}}),[]),{updateProviderOrdering:n}=(0,o.useDispatch)(a.paymentSettingsStore),[r,c]=(0,i.useState)(null);return(0,i.useEffect)((()=>{c(null)}),[e]),(0,m.jsx)(m.Fragment,{children:t?(0,m.jsx)(s.i,{rows:3}):(0,m.jsx)(g,{gateways:r||e,setGateways:function(e){const t=e.map((e=>e._order)).sort(((e,t)=>e-t)),o={};e.forEach(((e,n)=>{o[e.id]=t[n]})),n(o),c(e)}})})},w=u}}]);