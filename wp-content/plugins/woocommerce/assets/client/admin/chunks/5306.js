"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5306],{12974:(e,t,s)=>{s.d(t,{Ay:()=>a});var o=s(13240);const n=["a","b","em","i","strong","p","br"],r=["target","href","rel","name","download"],a=e=>({__html:(0,o.sanitize)(e,{ALLOWED_TAGS:n,ALLOWED_ATTR:r})})},1275:(e,t,s)=>{s.d(t,{v:()=>l});var o=s(18537),n=s(56427),r=s(86087),a=s(12974),i=s(1069),c=s(39793);const l=({method:e,paymentMethodsState:t,setPaymentMethodsState:s,isExpanded:l,initialVisibilityStatus:d,...m})=>{var u,p,_,h;const y=(0,r.useRef)(null);void 0===d&&null===y.current&&void 0!==t[e.id]&&(y.current=(0,i.TO)(e,t[e.id]));const g=void 0!==d?null!=d&&d:null!==(u=y.current)&&void 0!==u&&u;return l||g?(0,c.jsx)("div",{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done",...m,children:(0,c.jsxs)("div",{className:"woocommerce-list__item-inner",children:["apple_google"!==e.id&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,o.decodeEntities)(e.description))})]})]}),"apple_google"===e.id&&(0,c.jsxs)("div",{className:"woocommerce-list__item-multi",children:[(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row multi-row-space",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,o.decodeEntities)(e.description))})]})]}),(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.extraIcon,alt:e.extraTitle+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.extraTitle}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,o.decodeEntities)(null!==(p=e.extraDescription)&&void 0!==p?p:""))})]})]})]}),(0,c.jsx)("div",{className:"woocommerce-list__item-after",children:(0,c.jsx)("div",{className:"woocommerce-list__item-after__actions wc-settings-prevent-change-event",children:(0,c.jsx)(n.ToggleControl,{checked:null!==(_=t[e.id])&&void 0!==_&&_,onChange:o=>{s({...t,[e.id]:o})},disabled:null!==(h=e.required)&&void 0!==h&&h,label:""})})})]})}):null}},7175:(e,t,s)=>{s.d(t,{A:()=>i});var o=s(56427),n=s(47804),r=s(56109),a=s(39793);function i({onClose:e}){return(0,a.jsxs)("div",{className:"settings-payments-onboarding-modal__header",children:[(0,a.jsx)("img",{src:`${r.GZ}images/woo-logo.svg`,alt:"",role:"presentation",className:"settings-payments-onboarding-modal__header--logo"}),(0,a.jsx)(o.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:e,children:(0,a.jsx)(o.Icon,{icon:n.A})})]})}},58016:(e,t,s)=>{s.d(t,{A:()=>h});var o=s(27723),n=s(33068),r=s(51609),a=s(87007),i=s(86087),c=s(4921),l=s(56109),d=s(39793);function m({label:e,isCompleted:t,isActive:s}){return(0,d.jsxs)("div",{className:(0,c.A)("settings-payments-onboarding-modal__sidebar--list-item",{"is-active":s,"is-completed":t}),children:[(0,d.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-icon",children:t?(0,d.jsx)("img",{src:l.GZ+"images/onboarding/icons/complete.svg",alt:(0,o.__)("Step completed","woocommerce")}):(0,d.jsx)("img",{src:l.GZ+"images/onboarding/icons/pending.svg",alt:(0,o.__)("Step active","woocommerce")})}),(0,d.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-label",children:e})]})}var u=s(1069);function p({activeTopLevelStep:e,activeSubStep:t,steps:s,justCompletedStepId:n,includeSidebar:r=!1,sidebarTitle:a,context:c={}}){const l=s.find((t=>t.id===e));if((0,i.useEffect)((()=>{t&&(0,u.W7)("woopayments_onboarding_modal_step_view",{step:t.id,source:c?.sessionEntryPoint||"unknown"})}),[t]),!l)return null;const p=s.findIndex((t=>t.id===e))+1,_=e=>e.id===n||"completed"===e.status||p===s.length,h=s.sort(((e,t)=>{const s=_(e);return s===_(t)?0:s?-1:1}));return(0,d.jsxs)(d.Fragment,{children:[r&&(0,d.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar",children:[(0,d.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar--header",children:[(0,d.jsx)("h2",{className:"settings-payments-onboarding-modal__sidebar--header-title",children:a}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--header-steps",children:(0,o.sprintf)((0,o.__)("Step %1$s of %2$s","woocommerce"),p,s.length)})]}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--list",children:h.map((t=>(0,d.jsx)(m,{label:t.label,isCompleted:_(t),isActive:t.id===e},t.id)))})]}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__content",children:(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__step",id:t?.id,children:t?.content})})]})}var _=s(99096);function h({includeSidebar:e=!0}){const{steps:t,isLoading:s,currentTopLevelStep:i,currentStep:c,navigateToStep:l,justCompletedStepId:m,sessionEntryPoint:u}=(0,_.w)(),h=(0,n.zy)();return(0,r.useEffect)((()=>{var e;i&&!h.pathname.endsWith(null!==(e=i?.path)&&void 0!==e?e:"")&&l(i.id)}),[i,l,h.pathname]),s?(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__loading",children:(0,d.jsx)(a.A,{})}):t&&t.length>0?(0,d.jsx)(n.BV,{children:(0,d.jsx)(n.qh,{path:"*",element:(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__wrapper",children:(0,d.jsx)(p,{steps:t,activeTopLevelStep:null!==(y=i?.id)&&void 0!==y?y:"",activeSubStep:c,justCompletedStepId:m,includeSidebar:e,sidebarTitle:(0,o.__)("Set up WooPayments","woocommerce"),context:{sessionEntryPoint:u}})})})}):null;var y}},87007:(e,t,s)=>{s.d(t,{A:()=>n}),s(51609);var o=s(39793);const n=()=>(0,o.jsx)("svg",{className:"stripe-spinner",width:"29",height:"29",viewBox:"0 0 29 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M14.3308 28.3333C14.0453 28.3333 13.7714 28.2199 13.5695 28.018C13.3675 27.816 13.2541 27.5422 13.2541 27.2566C13.2541 26.971 13.3675 26.6972 13.5695 26.4952C13.7714 26.2933 14.0453 26.1799 14.3308 26.1799C17.4727 26.1799 20.4859 24.9317 22.7076 22.7101C24.9293 20.4884 26.1774 17.4752 26.1774 14.3333C26.1774 11.1914 24.9293 8.17821 22.7076 5.95655C20.4859 3.73489 17.4727 2.48677 14.3308 2.48677C12.5754 2.48495 10.8416 2.87419 9.25541 3.62623C7.66923 4.37826 6.27049 5.4742 5.16082 6.83441C5.07124 6.94388 4.96097 7.03464 4.83631 7.1015C4.71165 7.16836 4.57504 7.21001 4.43428 7.22407C4.15001 7.25248 3.8661 7.16679 3.645 6.98587C3.42391 6.80494 3.28374 6.54359 3.25534 6.25932C3.22694 5.97505 3.31262 5.69114 3.49355 5.47005C4.80533 3.86303 6.45849 2.56827 8.33301 1.67977C10.2075 0.791267 12.2564 0.331321 14.3308 0.333319C22.0626 0.333319 28.3308 6.6015 28.3308 14.3333C28.3308 22.0651 22.0626 28.3333 14.3308 28.3333Z",fill:"#4F575D"})})},99096:(e,t,s)=>{s.d(t,{X:()=>p,w:()=>u});var o=s(86087),n=s(47143),r=s(40314),a=s(96476),i=s(22861),c=s(39793);const l=(e,t)=>{for(const s of t){if(s.id===e)return s;if(s.subSteps){const t=l(e,s.subSteps);if(t)return t}}},d={buildStepURL:e=>(0,a.getNewPath)({path:e},e,{page:"wc-settings",tab:"checkout"}),preserveParams:["source","from"]},m=(0,o.createContext)({steps:[],isLoading:!0,currentStep:void 0,currentTopLevelStep:void 0,context:{},navigateToStep:()=>{},navigateToNextStep:()=>{},getStepByKey:()=>{},refreshStoreData:()=>{},closeModal:()=>{},justCompletedStepId:null,setJustCompletedStepId:()=>{},sessionEntryPoint:"",snackbar:{show:!1,duration:4e3,message:""},setSnackbar:()=>{}}),u=()=>(0,o.useContext)(m),p=({children:e,onboardingSteps:t,closeModal:s,onFinish:u,urlStrategy:p,sessionEntryPoint:_=i.Fx})=>{const h=(0,a.getHistory)(),[y,g]=(0,o.useState)([]),[w,b]=(0,o.useState)(!0),[v,x]=(0,o.useState)([]),[f,S]=(0,o.useState)(null),[j,k]=(0,o.useState)({show:!1,duration:4e3,message:""}),C=(0,o.useCallback)((e=>{S(e)}),[]),{invalidateResolutionForStoreSelector:N}=(0,n.useDispatch)(r.woopaymentsOnboardingStore),{invalidateResolutionForStoreSelector:T}=(0,n.useDispatch)(r.paymentSettingsStore),{storeData:F,isStoreLoading:P}=(0,n.useSelect)((e=>({storeData:e(r.woopaymentsOnboardingStore).getOnboardingData(_),isStoreLoading:e(r.woopaymentsOnboardingStore).isOnboardingDataRequestPending()})),[_]),E=(0,o.useCallback)(((e,t=v)=>l(e,t)),[v]),A=(0,o.useCallback)(((e,t)=>!e.dependencies||0===e.dependencies.length||e.dependencies.every((e=>{const s=l(e,t);return"completed"===s?.status}))),[]),B=(0,o.useCallback)(((e,t,s)=>"frontend"!==e.type?"completed"===e.status:t?"completed"===t.status||s&&"backend"===s.type&&("in_progress"===s.status||"completed"===s.status):!!e.subSteps?.length&&e.subSteps.every((e=>!!e.status&&"completed"===e.status))),[]),L=(0,o.useCallback)((e=>{const t=E(e);if(t?.path){const e=p||d,s=e.preserveParams?(0,a.getQuery)():{},o=e.preserveParams?.reduce(((e,t)=>(s[t]&&(e[t]=s[t]),e)),{})||{},n=e.buildStepURL(t.path,o);h.push(n)}}),[E,h,p]),D=(0,o.useCallback)(((e,t,s)=>{for(const[o,n]of e.entries()){if("frontend"===n.type){const t=e[o+1];if(B(n,s,t))continue}if("completed"!==n.status&&A(n,t)){if(n.subSteps&&n.subSteps.length>0){const e=D(n.subSteps,t,n);if(e)return e}return n}}}),[A]),I=D(v,v),W=(e=>{if(e){for(const t of v){if(t.id===e.id)return t;if(t.subSteps?.some((t=>t.id===e.id)))return t}return e}})(I),O=(0,o.useCallback)((()=>{if(!I)return void u?.();const e=(t,s)=>t.map((t=>{if(t.id===s)return{...t,status:"completed"};if(t.subSteps){const o=e(t.subSteps,s);if(o!==t.subSteps){const e=o.every((e=>"completed"===e.status));return{...t,subSteps:o,status:e?"completed":t.status}}}return t})),t=e(v,I.id),s=D(t,t);x(t),s?L(s.id):u?.()}),[I,v,L,D,u]),M=()=>{g([]),b(!0),C(null),x([]),k({show:!1,message:""}),N("getOnboardingData")};return(0,o.useEffect)((()=>{!P&&F.steps.length>0&&(g(F.steps),b(!1))}),[F,P]),(0,o.useEffect)((()=>{const e=t=>t.map((t=>{let s={...t};if("backend"===s.type){const e=y.find((e=>e.id===s.id));if(!e)return null;const t=e;s={...s,status:("started"===t.status?"in_progress":t.status)||"not_started",dependencies:t.dependencies||[],path:t.path,context:{...s.context||{},...t.context||{}},actions:t.actions,errors:t.errors}}return s.subSteps&&(s.subSteps=e(s.subSteps)),s})).filter((e=>null!==e)),s=e(t),o=(e,t,s)=>e.map(((n,r)=>{const a={...n};if("frontend"===a.type){a.subSteps?.length&&(a.subSteps=o(a.subSteps,t,a));const n=e[r+1],i=B(a,s,n);a.status=i?"completed":"not_started"}return a.subSteps&&"frontend"!==a.type&&(a.subSteps=o(a.subSteps,t,a)),a})),n=o(s,s);x(n)}),[y,A,t]),(0,o.useEffect)((()=>{M()}),[]),(0,c.jsx)(m.Provider,{value:{steps:v,context:F.context,isLoading:w,currentStep:I,currentTopLevelStep:W,navigateToStep:L,navigateToNextStep:O,getStepByKey:E,refreshStoreData:M,closeModal:()=>{s(),T("getPaymentProviders")},justCompletedStepId:f,setJustCompletedStepId:C,sessionEntryPoint:_,snackbar:j,setSnackbar:k},children:e})}},98404:(e,t,s)=>{s.d(t,{A:()=>re});var o=s(51609),n=s.n(o),r=s(99096),a=s(7175),i=s(66087),c=s(39793);const l=(e={})=>{const[t,s]=(0,o.useState)(e),[n,r]=(0,o.useState)({}),[a,c]=(0,o.useState)({});return{data:t,setData:e=>s((t=>({...t,...e}))),errors:n,setErrors:e=>r((t=>(0,i.omitBy)({...t,...e},i.isNil))),touched:a,setTouched:e=>c((t=>({...t,...e})))}},d=(0,o.createContext)(null),m=({children:e,initialData:t})=>(0,c.jsx)(d.Provider,{value:l(t),children:e}),u=()=>{const e=(0,o.useContext)(d);if(!e)throw new Error("useBusinessVerificationContext() must be used within <BusinessVerificationContextProvider>");return e};var p=s(56427),_=s(86087);const h=(0,o.createContext)(null),y=({children:e,onStepView:t,...s})=>{const a=(e=>e.reduce(((e,t,s)=>{var o;return n().isValidElement(t)&&(e[null!==(o=t.props.name)&&void 0!==o?o:s]=t),e}),{}))(e),i=(({steps:e,initialStep:t,onStepChange:s,onComplete:n,onExit:a})=>{const i=Object.keys(e),{currentStep:c}=(0,r.w)(),[l,d]=(0,o.useState)(null!=t?t:i[0]);if("completed"===c?.context?.sub_steps[l]?.status){const e=i.indexOf(l),t=i[e+1];d(t),s?.(t)}const m=(i.indexOf(l)+1)/i.length;return{currentStep:l,progress:m,nextStep:()=>{const e=i.indexOf(l),t=i[e+1];t?(d(t),s?.(t)):n?.()},prevStep:()=>{const e=i.indexOf(l),t=i[e-1];t?(d(t),s?.(t)):a?.()},exit:()=>a?.()}})({steps:a,...s});(0,_.useEffect)((()=>{t?.(i.currentStep)}),[i.currentStep]);const l=a[i.currentStep];return(0,c.jsx)(h.Provider,{value:i,children:l})},g=()=>{const e=(0,o.useContext)(h);if(!e)throw new Error("useStepperContext() must be used within <Stepper>");return e};var w=s(4921),b=s(24148),v=s(90700),x=s(72744),f=s(27723),S=s(21913);const j=e=>e?.name||"",k=({selectedItem:e},{type:t,changes:s,props:{items:o}})=>{switch(t){case S.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:o[e?Math.min(o.indexOf(e)+1,o.length-1):0]};case S.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:o[e?Math.max(o.indexOf(e)-1,0):o.length-1]};default:return s}},C=function({name:e,className:t,label:s,describedBy:o,options:n,onChange:r,value:a,placeholder:i,children:l}){const{getLabelProps:d,getToggleButtonProps:m,getMenuProps:u,getItemProps:h,isOpen:y,highlightedIndex:g,selectedItem:C}=(0,S.WM)({initialSelectedItem:n[0],items:n,itemToString:j,onSelectedItemChange:r,selectedItem:a||{},stateReducer:k}),N=j(C),T=u({className:"components-custom-select-control__menu","aria-hidden":!y}),F=(0,_.useCallback)((e=>{e.stopPropagation(),T?.onKeyDown?.(e)}),[T]);return T["aria-activedescendant"]?.startsWith("downshift-null")&&delete T["aria-activedescendant"],(0,c.jsxs)("div",{className:(0,w.A)("woopayments components-custom-select-control",t),children:[(0,c.jsx)("label",{...d({className:"components-custom-select-control__label"}),children:s}),(0,c.jsxs)(p.Button,{...m({"aria-label":s,"aria-labelledby":void 0,"aria-describedby":o||(N?(0,f.sprintf)((0,f.__)("Currently selected: %s","woocommerce"),N):(0,f.__)("No selection","woocommerce")),className:(0,w.A)("components-custom-select-control__button",{placeholder:!N}),name:e}),children:[(0,c.jsx)("span",{className:"components-custom-select-control__button-value",children:N||i}),(0,c.jsx)(b.A,{icon:v.A,className:"components-custom-select-control__button-icon"})]}),(0,c.jsx)("div",{...T,children:(0,c.jsx)("ul",{className:"components-custom-select-control__menu-container",onKeyDown:F,children:y&&n.map(((e,t)=>(0,c.jsxs)("li",{...h({item:e,index:t,className:(0,w.A)(e.className,"components-custom-select-control__item",{"is-highlighted":t===g}),style:e.style}),children:[l?l(e):e.name,e===C&&(0,c.jsx)(b.A,{icon:x.A,className:"components-custom-select-control__item-icon"})]},e.key)))})})]})};var N=s(56537);const T=({name:e,className:t,label:s,options:n,onChange:r,value:a,placeholder:i,searchable:l})=>{const d=(0,o.useRef)(null),m=(0,o.useRef)(),u=n.filter((e=>e.items?.length)).map((e=>e.key)),[p,_]=(0,o.useState)(new Set([u[0]])),[h,y]=(0,o.useState)(new Set([...u,...n[0]?.items||[]])),[g,j]=(0,o.useState)(""),k=n.filter((e=>h.has(e.key))),{isOpen:C,selectedItem:T,getToggleButtonProps:F,getMenuProps:P,getLabelProps:E,highlightedIndex:A,getItemProps:B}=(0,S.WM)({items:k,itemToString:e=>e?.name||"",selectedItem:a||{},onSelectedItemChange:r,stateReducer:(e,{changes:t,type:s})=>{if(l&&s===S.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown)return e;if(t.selectedItem&&t.selectedItem.items){if(g)return e;const s=t.selectedItem.key;return p.has(s)?(p.delete(s),t.selectedItem.items.forEach((e=>h.delete(e)))):(p.add(s),t.selectedItem.items.forEach((e=>h.add(e)))),_(p),y(h),e}return t}}),L=P({className:"components-grouped-select-control__list","aria-hidden":!C,onFocus:()=>d.current?.focus(),onBlur:e=>{e.relatedTarget===d.current&&(e.nativeEvent.preventDownshiftDefault=!0)},onKeyDown:e=>{"Space"===e.code&&(e.nativeEvent.preventDownshiftDefault=!0)}});return(0,c.jsxs)("div",{className:(0,w.A)("woopayments components-grouped-select-control",t),children:[(0,c.jsx)("label",{...E({className:"components-grouped-select-control__label"}),children:s}),(0,c.jsxs)("button",{...F({type:"button",className:(0,w.A)("components-text-control__input components-grouped-select-control__button",{placeholder:!T?.name}),name:e}),children:[(0,c.jsx)("span",{className:"components-grouped-select-control__button-value",children:T?.name||i}),(0,c.jsx)(b.A,{icon:v.A,className:"components-grouped-select-control__button-icon"})]}),(0,c.jsx)("div",{...L,children:C&&(0,c.jsxs)(c.Fragment,{children:[l&&(0,c.jsx)("input",{className:"components-grouped-select-control__search",ref:d,type:"text",value:g,onChange:({target:e})=>{if(m.current||(m.current={visibleItems:h}),""===e.value)y(m.current.visibleItems),m.current=void 0;else{const t=n.filter((t=>t?.group&&`${t.name} ${t.context||""}`.toLowerCase().includes(e.value.toLowerCase()))),s=t.map((e=>e?.group||"")),o=new Set([...t.map((e=>e.key)),...s]);y(o)}j(e.value)},tabIndex:-1,placeholder:(0,f.__)("Search…","woocommerce")}),(0,c.jsx)("ul",{className:"components-grouped-select-control__list-container",children:k.map(((e,t)=>{const s=!!e.items;return(0,c.jsxs)("li",{...B({item:e,index:t,className:(0,w.A)("components-grouped-select-control__item",e.className,{"is-highlighted":t===A},{"is-group":s})}),children:[(0,c.jsx)("div",{className:"components-grouped-select-control__item-content",children:e.name}),e.key===T?.key&&(0,c.jsx)(b.A,{icon:x.A}),!g&&s&&(0,c.jsx)(b.A,{icon:p.has(e.key)?N.A:v.A})]},e.key)}))})]})})]})},F=(e,t,s)=>{const{error:o,...n}=t;return o?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(e,{...n,ref:s,className:(0,w.A)(n.className,"has-error")}),(0,c.jsx)("div",{className:"components-form-field__error",children:o})]}):(0,c.jsx)(e,{...n,ref:s})},P=((0,o.forwardRef)(((e,t)=>F(p.TextControl,e,t))),e=>F(C,e)),E=e=>F(T,e),A={generic:{individual:(0,f.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,f.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,f.__)("Select if you run a non-business entity","woocommerce"),government_entity:(0,f.__)("Select if your business is classed as a government entity","woocommerce")},US:{individual:(0,f.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,f.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,f.__)("Select if you have been granted tax-exempt status by the Internal Revenue Service (IRS)","woocommerce"),government_entity:(0,f.__)("Select if your business is classed as a government entity","woocommerce")}},B=e=>{const t=window.wcSettings.admin?.onboarding?.profile?.industry?.[0];if(t)return e[t]},L=()=>{const{woocommerce_share_key:e,woocommerce_coming_soon:t,woocommerce_private_link:s}=window.wcSettings?.admin?.siteVisibilitySettings||{};return"yes"!==t||"no"===s?"":e?"?woo-share="+e:""};var D=s(1455),I=s.n(D);var W=s(36849);const O={steps:{activate:{heading:(0,f.__)("Start accepting real payments","woocommerce"),subheading:(0,W.A)({mixedString:(0,f.__)("You are currently testing payments on your store. To activate real payments, you will need to provide some additional details about your business. {{link}}Learn more{{/link}}.","woocommerce"),components:{link:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process"})}}),cta:(0,f.__)("Activate payments","woocommerce")},business:{heading:(0,f.__)("Let’s get your store ready to accept payments","woocommerce"),subheading:(0,f.__)("We’ll use these details to enable payments for your store. This information can’t be changed after your account is created.","woocommerce")},store:{heading:(0,f.__)("Please share a few more details","woocommerce"),subheading:(0,f.__)("This info will help us speed up the set up process.","woocommerce")},loading:{heading:(0,f.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,f.__)("This will take place in a secure environment through our partner. Once your business details are verified, you’ll be redirected back to your store dashboard.","woocommerce"),cta:(0,f.__)("Finish your verification process","woocommerce")},embedded:{heading:(0,f.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,f.__)("This info will verify your account","woocommerce")}},fields:{country:(0,f.__)("Where is your business located?","woocommerce"),business_type:(0,f.__)("What type of legal entity is your business?","woocommerce"),"company.structure":(0,f.__)("What category of legal entity identify your business?","woocommerce"),mcc:(0,f.__)("What type of goods or services does your business sell? ","woocommerce")},errors:{generic:(0,f.__)("Please provide a response","woocommerce"),country:(0,f.__)("Please provide a country","woocommerce"),business_type:(0,f.__)("Please provide a business type","woocommerce"),mcc:(0,f.__)("Please provide a type of goods or services","woocommerce")},placeholders:{generic:(0,f.__)("Select an option","woocommerce"),country:(0,f.__)("Select a country","woocommerce")},tos:(0,W.A)({mixedString:(0,f.sprintf)((0,f.__)("By using %1$s, you agree to be bound by our {{tosLink}}Terms of Service{{/tosLink}} (including {{merchantTermsLink}}%2$s merchant terms{{/merchantTermsLink}}) and acknowledge that you have read our {{privacyPolicyLink}}Privacy Policy{{/privacyPolicyLink}}.","woocommerce"),"WooPayments","WooPay"),components:{tosLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/"}),merchantTermsLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/#more-woopay-specifically"}),privacyPolicyLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://automattic.com/privacy/"})}}),continue:(0,f.__)("Continue","woocommerce"),back:(0,f.__)("Back","woocommerce"),cancel:(0,f.__)("Cancel","woocommerce")},M=e=>{const{data:t,errors:s,setErrors:n,touched:r,setTouched:a}=u(),i=(s=t[e])=>{r[e]||a({[e]:!0});const o=((e,t)=>!!t)(0,s)?void 0:O.errors[e]||O.errors.generic;n({[e]:o})};return(0,o.useEffect)((()=>(i(),t[e]||a({[e]:!1}),()=>n({[e]:void 0}))),[]),{validate:i,error:()=>r[e]?s[e]:void 0}};var R=s(1069);const z=({children:e})=>{const{data:t,errors:s,touched:n,setTouched:a}=u(),{currentStep:l,sessionEntryPoint:d}=(0,r.w)(),{nextStep:m}=g(),[_,h]=(0,o.useState)(!1);return(0,c.jsxs)("form",{onSubmit:async e=>{var o,r;e.preventDefault(),await((0,i.isEmpty)(s)&&(e=>["business_type","country","mcc"].every((t=>Boolean(e[t]))))(t)?(h(!0),((e,t,s)=>t?I()({url:t,method:"POST",data:{sub_steps:{...s,[e]:{status:"completed"}}}}):Promise.resolve())("business",null!==(o=l?.actions?.save?.href)&&void 0!==o?o:void 0,null!==(r=l?.context?.sub_steps)&&void 0!==r?r:{}).then((()=>((0,R.W7)("woopayments_onboarding_modal_kyc_sub_step_completed",{sub_step_id:"business",country:t.country||"unknown",business_type:t.business_type||"unknown",mcc:t.mcc||"unknown",source:d}),h(!1),m()))).catch((()=>{h(!1)}))):(a((0,i.mapValues)(n,(()=>!0))),Promise.resolve()))},children:[e,(0,c.jsx)(p.Button,{variant:"primary",type:"submit",className:"stepper__cta",onClick:()=>{var e;(0,R.W7)("woopayments_onboarding_modal_click",{step:null!==(e=l?.id)&&void 0!==e?e:"unknown",sub_step_id:"business",action:"business_form_continue",source:d})},isBusy:_,disabled:_,children:O.continue})]})},q=({onChange:e,...t})=>{var s;const{name:o}=t,{data:n,setData:r}=u(),{validate:a,error:i}=M(o);return(0,c.jsx)(P,{label:O.fields[o],value:t.options?.find((e=>e.key===n[o])),placeholder:null!==(s=O.placeholders[o])&&void 0!==s?s:O.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(o,t):r({[o]:t?.key}),a(t?.key)},options:[],error:i(),...t})},H=({onChange:e,...t})=>{var s;const{name:o}=t,{data:n,setData:r}=u(),{validate:a,error:i}=M(o);return(0,c.jsx)(E,{label:O.fields[o],value:t.options?.find((e=>e.key===n[o])),placeholder:null!==(s=O.placeholders[o])&&void 0!==s?s:O.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(o,t):r({[o]:t?.key}),a(t?.key)},options:[],error:i(),...t})},U=()=>{var e;const{data:t,setData:s}=u(),{currentStep:o,sessionEntryPoint:n}=(0,r.w)(),a=(e=>Object.entries(e||[]).map((([e,t])=>({key:e,name:t,types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name))))(o?.context?.fields?.available_countries||{}),i=(e=>(e||[]).map((e=>({...e,types:e.types.map((t=>({...t,description:A[e.key]?A[e.key][t.key]:A.generic[t.key]})))}))).sort(((e,t)=>e.name.localeCompare(t.name)))||[])(o?.context?.fields?.business_types||[]),l=((null!==(e=o?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[])||[]).filter((e=>!!e?.items&&(e.items?.filter((e=>!e?.items))||[]).length)).reduce(((e,t)=>{const s=t.items?.map((e=>({key:e.id,name:e.title,group:t.id,context:e?.keywords?e.keywords.join(" "):""})))||[];return[...e,{key:t.id,name:t.title,items:s.map((e=>e.key))},...s]}),[]),d=i.find((e=>"PR"===t.country?"US"===e.key:e.key===t.country)),m=d?.types.sort(((e,t)=>"company"===e.key?-1:"company"===t.key?1:0)),p=m?.find((e=>e.key===t.business_type)),_=0===p?.structures.length||p?.structures.find((e=>e.key===t["company.structure"])),h=e=>{s(e);const t=o?.actions?.save?.href;return t?I()({url:t,method:"POST",data:{self_assessment:e,source:n}}):Promise.resolve()},y=(e,t)=>{let s={[e]:t?.key};return"business_type"===e?s={...s,"company.structure":void 0}:"country"===e&&(s={...s,business_type:void 0}),h(s)};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{"data-testid":"country-select",children:(0,c.jsx)(q,{name:"country",options:a,onChange:y})}),d&&d.types.length>0&&(0,c.jsx)("span",{"data-testid":"business-type-select",children:(0,c.jsx)(q,{name:"business_type",options:d.types,onChange:y,children:e=>(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{children:e.name}),(0,c.jsx)("div",{className:"complete-business-info-task__option-description",children:e.description})]})})}),p&&p.structures.length>0&&(0,c.jsx)("span",{"data-testid":"business-structure-select",children:(0,c.jsx)(q,{name:"company.structure",options:p.structures,onChange:y})}),d&&p&&_&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{"data-testid":"mcc-select",children:(0,c.jsx)(H,{name:"mcc",options:l,onChange:(e,s)=>{const o={...t,[e]:s?.key};return h(o)},searchable:!0})}),(0,c.jsx)("span",{className:"woopayments-onboarding__tos",children:O.tos})]})]})};var V=s(87007),K=s(20195),G=s(73290),Y=s(94736),$=s(92991),Z=s(8181);x.A,$.A,Y.A,G.A;const X=({children:e,actions:t=[],className:s,status:o="info",isDismissible:n=!0,onRemove:r})=>{((e,t)=>{const s="string"==typeof t?t:(0,_.renderToString)(t),o="error"===e?"assertive":"polite";(0,_.useEffect)((()=>{s&&(0,K.speak)(s,o)}),[s,o])})(o,e);const a=(0,w.A)(s,"woopayments-banner-notice","is-"+o);return(0,c.jsxs)("div",{className:a,children:[(0,c.jsxs)("div",{className:"woopayments-banner-notice__content",children:[e,t.length>0&&(0,c.jsx)("div",{className:"woopayments-banner-notice__actions",children:t.map((({className:e,label:t,variant:s,onClick:o,url:n,urlTarget:r},a)=>{let i=s;return"primary"!==s&&(i=n?"link":"secondary"),(0,c.jsx)(p.Button,{href:n,variant:i,onClick:n?void 0:o,className:e,target:r,children:t},a)}))})]}),n&&(0,c.jsx)(p.Button,{className:"woopayments-banner-notice__dismiss",icon:(0,c.jsx)(Z.A,{}),label:(0,f.__)("Dismiss this notice","woocommerce"),onClick:()=>r?.(),showTooltip:!1})]})};var J=s(2929),Q=s(86948);const ee={variables:{colorPrimary:"#873EFF",colorBackground:"#FFFFFF",buttonPrimaryColorBackground:"#3858E9",buttonPrimaryColorBorder:"#3858E9",buttonPrimaryColorText:"#FFFFFF",buttonSecondaryColorBackground:"#FFFFFF",buttonSecondaryColorBorder:"#3858E9",buttonSecondaryColorText:"#3858E9",colorText:"#101517",colorSecondaryText:"#50575E",actionPrimaryColorText:"#3858E9",actionSecondaryColorText:"#101517",colorBorder:"#DDDDDD",formHighlightColorBorder:"#3858E9",formAccentColor:"#3858E9",colorDanger:"#CC1818",offsetBackgroundColor:"#F0F0F0",formBackgroundColor:"#FFFFFF",badgeNeutralColorText:"#2C3338",badgeNeutralColorBackground:"#F6F7F7",badgeNeutralColorBorder:"#F6F7F7",badgeSuccessColorText:"#005C12",badgeSuccessColorBackground:"#EDFAEF",badgeSuccessColorBorder:"#EDFAEF",badgeWarningColorText:"#614200",badgeWarningColorBackground:"#FCF9E8",badgeWarningColorBorder:"#FCF9E8",badgeDangerColorText:"#8A2424",badgeDangerColorBackground:"#FCF0F1",badgeDangerColorBorder:"#FCF0F1",borderRadius:"2px",buttonBorderRadius:"2px",formBorderRadius:"2px",badgeBorderRadius:"2px",overlayBorderRadius:"8px",spacingUnit:"10px",fontFamily:"-apple-system, BlinkMacSystemFont, 'system-ui', 'Segoe UI', 'Helvetica Neue', 'Helvetica', 'Roboto', 'Arial', sans-serif",fontSizeBase:"16px",headingXlFontSize:"32px",headingXlFontWeight:"400",headingLgFontSize:"24px",headingLgFontWeight:"400",headingMdFontSize:"20px",headingMdFontWeight:"400",headingSmFontSize:"13px",headingSmFontWeight:"600",headingXsFontSize:"12px",headingXsFontWeight:"600",bodyMdFontWeight:"400",bodyMdFontSize:"16px",bodySmFontSize:"13px",bodySmFontWeight:"400",labelSmFontSize:"12px",labelSmFontWeight:"200",labelMdFontSize:"13px"}},te=({onboardingData:e,onExit:t,onLoaderStart:s,onLoadError:n,onStepChange:a,collectPayoutRequirements:l=!1})=>{const{stripeConnectInstance:d,initializationError:m}=(e=>{const[t,s]=(0,o.useState)(null),{currentStep:n,sessionEntryPoint:a}=(0,r.w)(),[c,l]=(0,o.useState)(null),[d,m]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{(async()=>{try{var t;const o=await(async(e,t,s)=>{const o=(n=e,(0,i.toPairs)(n).reduce(((e,[t,s])=>null!==s?(0,i.set)(e,t,s):e),{}));var n;const r={};return Object.keys(o).length>0&&(r.self_assessment=o),s&&(r.source=s),await I()({url:t,method:"POST",data:r})})(e,null!==(t=n?.actions?.kyc_session?.href)&&void 0!==t?t:"",a),{clientSecret:r,publishableKey:c}=o.session;if(!c)throw new Error((0,f.__)("Unable to start the business verification session. If this problem persists, please contact support.","woocommerce"));const l=(0,J.e)({publishableKey:c,fetchClientSecret:async()=>r,appearance:{overlays:"drawer",...ee},locale:o.session.locale.replace("_","-")});s(l)}catch(e){l(e instanceof Error?e.message:(0,f.__)("Unable to start the business verification session. If this problem persists, please contact support.","woocommerce"))}finally{m(!1)}})()}),[e]),{stripeConnectInstance:t,initializationError:c,loading:d}})(e);return(0,c.jsxs)(c.Fragment,{children:[m&&(0,c.jsx)(X,{status:"error",children:m}),d&&(0,c.jsx)(Q.MT,{connectInstance:d,children:(0,c.jsx)(Q.hw,{onLoaderStart:s,onLoadError:n,onExit:t,onStepChange:e=>a?.(e.step),collectionOptions:{fields:l?"eventually_due":"currently_due",futureRequirements:"omit"}})})]})},se=({collectPayoutRequirements:e=!1})=>{var t;const{data:s}=u(),{currentStep:n,navigateToNextStep:a,sessionEntryPoint:i}=(0,r.w)(),[l,d]=(0,o.useState)(!1),[m,p]=(0,o.useState)(!0),[_,h]=(0,o.useState)(null),y=null!==(t=n?.actions?.kyc_fallback?.href)&&void 0!==t?t:"";return(0,c.jsxs)(c.Fragment,{children:[_&&("invalid_request_error"===_.error.type?(0,c.jsx)(X,{className:"woopayments-banner-notice--embedded-kyc",status:"warning",isDismissible:!1,actions:[{label:"Learn more",variant:"primary",url:"https://woocommerce.com/document/woopayments/startup-guide/#requirements",urlTarget:"_blank"},{label:"Cancel",variant:"link",url:y}],children:(0,f.__)("Payment activation through our financial partner requires HTTPS and cannot be completed.","woocommerce")}):(0,c.jsx)(X,{className:"woopayments-banner-notice--embedded-kyc",status:"error",isDismissible:!1,children:_.error.message})),m&&(0,c.jsx)("div",{className:"embedded-kyc-loader-wrapper padded",children:(0,c.jsx)(V.A,{})}),l&&(0,c.jsx)("div",{className:"embedded-kyc-loader-wrapper",children:(0,c.jsx)(V.A,{})}),(0,c.jsx)(te,{onExit:async()=>{d(!0);try{var e;(await(async(e,t)=>await I()({url:e,method:"POST",data:{source:t}}))(null!==(e=n?.actions?.kyc_session_finish?.href)&&void 0!==e?e:"",i)).success?a():window.location.href=y}catch(e){window.location.href=y}},onStepChange:t=>{(0,R.W7)("woopayments_onboarding_modal_kyc_step_change",{kyc_step_id:t,collect_payout_requirements:e,source:i})},onLoaderStart:()=>{(0,R.W7)("woopayments_onboarding_modal_kyc_started_loading",{collect_payout_requirements:e,source:i}),p(!1)},onLoadError:t=>{(0,R.W7)("woopayments_onboarding_modal_kyc_load_error",{error_type:t.error.type,error_message:t.error.message||"no_message",collect_payout_requirements:e,source:i}),h(t)},onboardingData:s,collectPayoutRequirements:e})]})},oe=()=>{const{currentStep:e,sessionEntryPoint:t,refreshStoreData:s}=(0,r.w)(),{nextStep:n}=g(),[a,i]=(0,o.useState)(!1);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("h1",{className:"stepper__heading",children:O.steps.activate.heading}),(0,c.jsx)("p",{className:"stepper__subheading",children:O.steps.activate.subheading}),(0,c.jsx)("div",{className:"stepper__content",children:(0,c.jsx)(p.Button,{variant:"primary",className:"stepper__cta",onClick:()=>{if((0,R.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",sub_step_id:"activate",action:"activate_payments",source:t}),!e?.actions?.test_account_disable?.href)return n();i(!0),I()({url:e?.actions?.test_account_disable?.href,method:"POST",data:{from:"step_"+(e?.id||"unknown"),source:t}}).then((async()=>(await("function"==typeof s?s():Promise.resolve()),i(!1),n()))).catch((()=>{i(!1)}))},isBusy:a,disabled:a,children:O.steps.activate.cta})})]})},ne=({name:e,children:t,showHeading:s=!0})=>(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"stepper__wrapper",children:[s&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("h1",{className:"stepper__heading",children:O.steps[e].heading}),(0,c.jsx)("h2",{className:"stepper__subheading",children:O.steps[e].subheading})]}),(0,c.jsx)("div",{className:"stepper__content",children:t})]})}),re=()=>{var e,t,s,o;const{currentStep:n,closeModal:i,sessionEntryPoint:l}=(0,r.w)(),d={business_name:window.wcSettings?.siteTitle,mcc:B(null!==(e=n?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[]),site:"localhost"===location.hostname?"https://wcpay.test":window.wcSettings?.homeUrl+L(),country:n?.context?.fields?.location,...null!==(t=n?.context?.self_assessment)&&void 0!==t?t:{}},u=null!==(s=n?.context?.has_test_account)&&void 0!==s&&s,p=null!==(o=n?.context?.has_sandbox_account)&&void 0!==o&&o,_=u||p&&"not_started"===n?.status,h=[..._?["activate"]:[],"business","embedded"].find((e=>"completed"!==n?.context?.sub_steps[e]?.status));return(0,c.jsxs)("div",{className:"settings-payments-onboarding-modal__step-business-verification",children:[(0,c.jsx)(a.A,{onClose:i}),(0,c.jsx)("div",{className:"settings-payments-onboarding-modal__step-business-verification-content",children:(0,c.jsx)(m,{initialData:d,children:(0,c.jsxs)(y,{initialStep:h,onStepView:e=>{(0,R.W7)("woopayments_onboarding_modal_step_view",{step:n?.id||"unknown",sub_step_id:e,source:l})},onStepChange:()=>{window.scroll(0,0)},onExit:()=>{(0,R.W7)("woopayments_onboarding_modal_step_exit",{step:n?.id||"unknown",source:l})},onComplete:()=>{(0,R.W7)("woopayments_onboarding_modal_step_complete",{step:n?.id||"unknown",source:l})},children:[_&&(0,c.jsx)(ne,{name:"activate",showHeading:!1,children:(0,c.jsx)(oe,{})}),(0,c.jsx)(ne,{name:"business",children:(0,c.jsx)(z,{children:(0,c.jsx)(U,{})})}),(0,c.jsx)(ne,{name:"embedded",showHeading:!1,children:(0,c.jsx)(se,{})})]})})})]})}},8148:(e,t,s)=>{s.d(t,{A:()=>l}),s(51609);var o=s(27723),n=s(56427),r=s(99096),a=s(7175),i=s(1069),c=s(39793);const l=()=>{const{context:e,closeModal:t,sessionEntryPoint:s}=(0,r.w)();return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{onClose:t}),(0,c.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,c.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-finish",children:[(0,c.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-finish-title",children:(0,o.__)("You’re ready to accept payments!","woocommerce")}),(0,c.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-finish-description",children:(0,o.__)("Great news — your WooPayments account has been activated. You can now start accepting payments on your store.","woocommerce")}),(0,c.jsx)(n.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-finish-primary-button",onClick:()=>{var t;(0,i.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"go_to_payments_overview",source:s}),window.location.href=null!==(t=e?.urls?.overview_page)&&void 0!==t?t:""},children:(0,o.__)("Go to Payments Overview","woocommerce")}),(0,c.jsxs)("div",{className:"divider",children:[(0,c.jsx)("span",{className:"divider-line"}),(0,c.jsx)("span",{className:"divider-text",children:(0,o.__)("OR","woocommerce")}),(0,c.jsx)("span",{className:"divider-line"})]}),(0,c.jsx)(n.Button,{variant:"secondary",className:"settings-payments-onboarding-modal__step--content-finish-secondary-button",onClick:()=>{(0,i.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"close_window",source:s}),t()},children:(0,o.__)("Close this window","woocommerce")})]})})]})}},91289:(e,t,s)=>{s.d(t,{A:()=>h});var o=s(27723),n=s(56427),r=s(83306),a=s(86087),i=s(1455),c=s.n(i),l=s(47804),d=s(4921),m=s(99096),u=s(1275),p=s(1069),_=s(39793);function h(){const{currentStep:e,navigateToNextStep:t,closeModal:s,sessionEntryPoint:i}=(0,m.w)(),[h,y]=(0,a.useState)(!1),[g,w]=(0,a.useState)({}),[b,v]=(0,a.useState)(null),[x,f]=(0,a.useState)(!1),S=e?.context?.pms_state,j=e?.context?.recommended_pms,k=(0,a.useMemo)((()=>j?(0,p.js)(j):[]),[j]),C=(0,a.useRef)(null),[N,T]=(0,a.useState)(!1);(0,a.useEffect)((()=>{S&&w(S)}),[S]);const F=(0,a.useMemo)((()=>(0,p.LI)(g)),[g]);(0,a.useEffect)((()=>{if(null===b&&k.length>0&&Object.keys(F).length>0&&k.every((e=>void 0!==F[e.id]))){const e={};k.forEach((t=>{e[t.id]=(0,p.TO)(t,F[t.id])})),v(e)}}),[k,F,b]);const P=(0,a.useMemo)((()=>!b||h?0:k.filter((e=>{var t;return!(null!==(t=b[e.id])&&void 0!==t&&t)})).length),[k,h,b]),E=t=>{const s=e?.actions?.save?.href;return s?c()({url:s,method:"POST",data:{payment_methods:t,source:i}}).then((()=>{w(t)})):(w(t),Promise.resolve())},A=()=>setTimeout((()=>{const e=C.current;if(e){const t=e.scrollHeight>e.clientHeight;T(t)}}),10);return(0,a.useEffect)((()=>{let e=A();const t=()=>{clearTimeout(e),e=A()};return window.addEventListener("resize",t),()=>{clearTimeout(e),window.removeEventListener("resize",t)}}),[h,b]),(0,_.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods",children:[(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods__header",children:[(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods__header--title",children:[(0,_.jsx)("h1",{className:"components-truncate components-text",children:(0,o.__)("Choose your payment methods","woocommerce")}),(0,_.jsx)(n.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:s,children:(0,_.jsx)(n.Icon,{icon:l.A})})]}),(0,_.jsx)("div",{className:"woocommerce-recommended-payment-methods__header--description",children:(0,o.__)("Select which payment methods you'd like to offer to your shoppers. You can update these at any time.","woocommerce")})]}),(0,_.jsx)("div",{className:"woocommerce-recommended-payment-methods__list",children:(0,_.jsxs)("div",{className:"settings-payments-methods__container",ref:C,children:[(0,_.jsx)("div",{className:"woocommerce-list",children:k?.map((e=>{var t;return(0,_.jsx)(u.v,{method:e,paymentMethodsState:(0,p.LI)(g),setPaymentMethodsState:e=>{E(e)},initialVisibilityStatus:b&&null!==(t=b[e.id])&&void 0!==t?t:null,isExpanded:h},e.id)}))}),!h&&P>0&&(0,_.jsx)("div",{className:"settings-payments-methods__show-more--wrapper",children:(0,_.jsx)(n.Button,{className:"settings-payments-methods__show-more",onClick:()=>{(0,p.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"show_more",hidden_count:P,source:i}),y(!h)},tabIndex:0,"aria-expanded":h,children:(0,o.sprintf)((0,o.__)("Show more (%s)","woocommerce"),P)})})]})}),(0,_.jsx)("div",{className:(0,d.A)("woocommerce-recommended-payment-methods__list_footer",{"has-border":N}),children:(0,_.jsx)(n.Button,{className:"components-button is-primary",onClick:()=>{const s=e?.actions?.finish?.href;s&&(f(!0),E(g).then((()=>c()({url:s,method:"POST",data:{source:i}}))).then((()=>{var s;const o=Object.keys(b||{}),n=Object.keys(g),a={displayed_payment_methods:o.join(", "),default_displayed_pms:o.filter((e=>!1!==b?.[e])).join(", "),default_selected_pms:k.filter((e=>e.enabled)).map((e=>e.id)).join(", "),selected_payment_methods:n.filter((e=>g[e])).join(", "),deselected_payment_methods:n.filter((e=>!g[e])).join(", "),business_country:null!==(s=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code)&&void 0!==s?s:"unknown",source:i};(0,p.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"continue",...a}),(0,r.recordEvent)("wcpay_settings_payment_methods_continue",a),f(!1),t()})).catch((()=>{f(!1)})))},isBusy:x,disabled:x,children:(0,o.__)("Continue","woocommerce")})})]})})}},33623:(e,t,s)=>{s.d(t,{A:()=>w});var o=s(51609),n=s(1455),r=s.n(n),a=s(85816),i=s(27723),c=s(56427),l=s(96476),d=s(7175),m=s(99096),u=s(56109),p=s(1069),_=s(68345),h=s(39793);const y=({progress:e,title:t,message:s})=>(0,h.jsx)(a.Loader,{className:"woocommerce-payments-test-account-step__preloader",children:(0,h.jsxs)(a.Loader.Layout,{className:"woocommerce-payments-test-account-step__preloader-layout",children:[(0,h.jsx)(a.Loader.Illustration,{children:(0,h.jsx)("img",{src:`${u.GZ}images/onboarding/test-account-setup.svg`,alt:(0,i.__)("Setup","woocommerce"),style:{maxWidth:"223px"}})}),(0,h.jsx)(a.Loader.Title,{children:t||(0,i.__)("Finishing payments setup","woocommerce")}),(0,h.jsx)(a.Loader.ProgressBar,{progress:null!=e?e:0}),(0,h.jsx)(a.Loader.Sequence,{interval:0,children:s||(0,i.__)("In just a few moments, you'll be ready to test payments on your store.","woocommerce")})]})}),g=[(0,i.__)("Setting up your test account","woocommerce"),(0,i.__)("Finishing payments setup","woocommerce"),(0,i.__)("Almost there!","woocommerce")],w=()=>{const{currentStep:e,closeModal:t,setJustCompletedStepId:s,sessionEntryPoint:n,setSnackbar:a}=(0,m.w)(),[u,w]=(0,o.useState)("idle"),[b,v]=(0,o.useState)(20),[x,f]=(0,o.useState)(),[S,j]=(0,o.useState)(0),[k,C]=(0,o.useState)(0),[N,T]=(0,o.useState)(g[0]),[F,P]=(0,o.useState)(!1),[E,A]=(0,o.useState)(),B=(0,o.useRef)(null),L=(0,o.useRef)(null),D=(0,o.useRef)(null),I=(0,o.useRef)(0);(0,o.useEffect)((()=>{if("success"===u&&(0,p.W7)("woopayments_onboarding_modal_step_view",{step:e?.id||"unknown",sub_step_id:"ready_to_test_payments",source:n}),"polling"!==u&&"initializing"!==u)return void(I.current=0);0===I.current&&T(g[0]);const t=setTimeout((()=>{I.current+=1,I.current<g.length&&T(g[I.current])}),5e3);return()=>{clearTimeout(t)}}),[u]);const W=()=>{null!==B.current&&(clearTimeout(B.current),B.current=null),null!==D.current&&(clearTimeout(D.current),D.current=null)},O=(0,o.useCallback)((()=>{w("idle"),v(0),f(void 0),j(0),L.current=null,W()}),[w,v,f,j]);(0,o.useEffect)((()=>{if("idle"===u){if("completed"===e?.status)return w("success"),s(e.id),void v(100);if("blocked"===e?.status)return f(e?.errors?.[0]?.message||(0,i.__)("There are environment or store setup issues which are blocking progress. Please resolve them to proceed.","woocommerce")),void w("blocked");"not_started"===e?.status||"failed"===e?.status?(w("initializing"),v(10),(async()=>{e?.actions?.clean?.href&&(k>0||"failed"===e?.status)&&await r()({url:e?.actions?.clean?.href,method:"POST"})})().then((()=>r()({url:e?.actions?.init?.href,method:"POST",data:{source:n}}))).then((e=>{e?.success?w("polling"):(A(e?.code||""),f(e?.message||(0,i.__)("Creating test account failed. Please try again.","woocommerce")),w("error"))})).catch((e=>{A(e?.code||""),f(e.message),w("error")}))):w("polling")}if("polling"===u){const t=()=>{W(),r()({url:e?.actions?.check?.href,method:"POST"}).then((o=>{if("completed"===o?.status)return void(B.current=window.setTimeout((()=>{w("success"),v(100),s(e?.id||"")}),1e3));let n,r,a=0;v((e=>(a=0===S?Math.min(e+5,90):1===S?Math.min(e+1,96):e,a))),0===S&&a>=90?(n=1,r=5e3,L.current=Date.now()):1===S?L.current&&Date.now()-L.current>3e4?(n=2,r=7e3):(n=1,r=5e3):2===S?(n=2,r=7e3):(n=0,r=3e3),j(n),B.current=window.setTimeout(t,r)})).catch((e=>{f(e.message),w("error"),W()}))};t()}return"initializing"===u&&null===D.current&&(D.current=window.setInterval((()=>{v((e=>e<30?Math.min(e+2,30):e))}),1e3)),"initializing"!==u&&null!==D.current&&(clearTimeout(D.current),D.current=null),()=>{W()}}),[u,e,k,S,s]),(0,o.useEffect)((()=>{"success"===u&&(0,l.navigateTo)({url:(0,l.getNewPath)({nox:"test_account_created"},"",{page:"wc-admin"})})}),[u]);const M="woocommerce_woopayments_test_account_already_exists"===E?[{label:(0,i.__)("Reset Account","woocommerce"),variant:"secondary",onClick:()=>{P(!0)}}]:[{label:(0,i.__)("Try Again","woocommerce"),variant:"primary",onClick:()=>{(0,p.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"try_again_on_error",retries:k+1,source:n}),O(),C((e=>e+1))}},{label:(0,i.__)("Cancel","woocommerce"),variant:"secondary",className:"woocommerce-payments-test-account-step__error-cancel-button",onClick:()=>{(0,p.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"cancel_on_error",retries:k,source:n}),t()}}];return(0,h.jsxs)("div",{className:"woocommerce-payments-test-account-step",children:[(0,h.jsx)(d.A,{onClose:t}),("error"===u||"blocked"===u)&&(0,h.jsx)(c.Notice,{status:"blocked"===u?"error":"warning",isDismissible:!1,actions:"blocked"!==u?M:[],className:"woocommerce-payments-test-account-step__error",children:(0,h.jsx)("p",{className:"woocommerce-payments-test-account-step__error-message",children:x||(0,i.__)("An error occurred while creating your test account. Please try again.","woocommerce")})}),("initializing"===u||"polling"===u||"success"===u)&&(0,h.jsx)(y,{progress:b,title:N,message:(R=S,1===R?(0,i.__)("The test account creation is taking a bit longer than expected, but don't worry — we're on it! Please bear with us for a few seconds more as we set everything up for your store.","woocommerce"):2===R?(0,i.__)("Thank you for your patience! Unfortunately, the test account creation is taking a bit longer than we anticipated. But don't worry — we won't give up! Feel free to close this modal and check back later. We appreciate your understanding!","woocommerce"):void 0)}),(0,h.jsx)(_.M,{isOpen:F,onClose:()=>{P(!1),a({show:!0,message:(0,i.__)("Your test account was successfully reset.","woocommerce")})},isEmbeddedResetFlow:!0,resetUrl:e?.actions?.reset?.href})]});var R}},59530:(e,t,s)=>{s.d(t,{A:()=>y});var o=s(51609),n=s(27723),r=s(36849),a=s(56427),i=s(98846),c=s(1455),l=s.n(c),d=s(7175),m=s(99096),u=s(56109),p=s(1069),_=s(75854),h=s(39793);const y=()=>{const{closeModal:e,currentStep:t,sessionEntryPoint:s,navigateToNextStep:c,refreshStoreData:y,getStepByKey:g}=(0,m.w)(),[w,b]=(0,o.useState)(!1);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.A,{onClose:e}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,h.jsx)("div",{className:"woocommerce-payments-test-or-live-account-step__success_content_container",children:(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content woocommerce-payments-test-or-live-account-step__success_content",children:[(0,h.jsx)("h1",{className:"woocommerce-payments-test-or-live-account-step__success_content_title",children:(0,n.__)("You're almost there — time to activate payments!","woocommerce")}),(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,h.jsx)("p",{children:(0,n.__)("Activate payments to accept real orders and process transactions.","woocommerce")})})}),(0,h.jsxs)("div",{className:"woocommerce-payments-test-or-live-account-step__success-whats-next",children:[(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:u.GZ+"images/icons/dollar.svg",alt:"",role:"presentation"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,n.__)("Activate real payments","woocommerce")}),(0,h.jsx)("div",{children:(0,r.A)({mixedString:(0,n.__)("Provide some additional details about your business to process real transactions. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,h.jsx)(i.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})]})]}),(0,h.jsx)(a.Button,{variant:"primary",onClick:()=>{b(!0),(0,p.W7)("woopayments_onboarding_modal_click",{step:t?.id||"unknown",action:"activate_payments",source:s});const e=g(_.CX),o=e?.actions?.finish?.href;o&&l()({url:o,method:"POST"}).then((()=>{b(!1),y()})).catch((()=>{b(!1)}))},isBusy:w,disabled:w,children:(0,n.__)("Start accepting payments","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-payments-test-or-live-account-step__success_content_or-divider",children:[(0,h.jsx)("hr",{}),(0,n.__)("OR","woocommerce"),(0,h.jsx)("hr",{})]}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:u.GZ+"images/icons/post-list.svg",alt:"",role:"presentation"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,n.__)("Test payments first, activate later","woocommerce")}),(0,h.jsx)("div",{children:(0,h.jsx)("p",{children:(0,r.A)({mixedString:(0,n.__)("A test account will be created for you to {{link}}test payments on your store{{/link}}. You'll need to activate payments later to process real transactions.","woocommerce"),components:{link:(0,h.jsx)(i.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/sandbox-mode/",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]}),(0,h.jsx)(a.Button,{variant:"secondary",isBusy:w,disabled:w,onClick:()=>{c()},children:(0,n.__)("Test payments","woocommerce")})]})]})})})]})}},10432:(e,t,s)=>{s.d(t,{A:()=>u}),s(51609);var o=s(27723),n=s(56427),r=s(86087),a=s(1455),i=s.n(a),c=s(99096),l=s(7175),d=s(1069),m=s(39793);const u=()=>{const{currentStep:e,closeModal:t,sessionEntryPoint:s}=(0,c.w)(),[a,u]=(0,r.useState)(!1);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.A,{onClose:t}),(0,m.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,m.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-jetpack",children:[(0,m.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-jetpack-title",children:(0,o.__)("Connect to WordPress.com","woocommerce")}),(0,m.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-jetpack-description",children:(0,o.__)("You’ll be briefly redirected to connect your store to your WordPress.com account and unlock the full features and functionality of WooPayments","woocommerce")}),(0,m.jsx)(n.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-jetpack-button",isBusy:a,disabled:a,onClick:()=>{var t;u(!0);const o=e?.actions?.start?.href;o&&i()({url:o,method:"POST",data:{source:s}}),(0,d.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"connect_to_wpcom",source:s}),window.location.href=null!==(t=e?.actions?.auth?.href)&&void 0!==t?t:""},children:(0,o.__)("Connect","woocommerce")})]})})]})}}}]);