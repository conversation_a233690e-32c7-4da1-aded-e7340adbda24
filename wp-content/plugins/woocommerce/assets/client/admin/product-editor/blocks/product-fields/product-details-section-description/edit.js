"use strict";var __createBinding=this&&this.__createBinding||(Object.create?function(e,t,o,r){void 0===r&&(r=o);var n=Object.getOwnPropertyDescriptor(t,o);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[o]}}),Object.defineProperty(e,r,n)}:function(e,t,o,r){void 0===r&&(r=o),e[r]=t[o]}),__setModuleDefault=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),__importStar=this&&this.__importStar||function(){var e=function(t){return e=Object.getOwnPropertyNames||function(e){var t=[];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[t.length]=o);return t},e(t)};return function(t){if(t&&t.__esModule)return t;var o={};if(null!=t)for(var r=e(t),n=0;n<r.length;n++)"default"!==r[n]&&__createBinding(o,t,r[n]);return __setModuleDefault(o,t),o}}(),__importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.ProductDetailsSectionDescriptionBlockEdit=ProductDetailsSectionDescriptionBlockEdit;const clsx_1=__importDefault(require("clsx")),components_1=require("@wordpress/components"),data_1=require("@wordpress/data"),element_1=require("@wordpress/element"),i18n_1=require("@wordpress/i18n"),icons=__importStar(require("@wordpress/icons")),block_templates_1=require("@woocommerce/block-templates"),navigation_1=require("@woocommerce/navigation"),tracks_1=require("@woocommerce/tracks"),core_data_1=require("@wordpress/core-data"),block_slot_fill_1=require("../../../components/block-slot-fill"),validation_context_1=require("../../../contexts/validation-context"),constants_1=require("../../../constants"),use_error_handler_1=require("../../../hooks/use-error-handler"),wooIcons=__importStar(require("../../../icons")),is_product_form_template_system_enabled_1=__importDefault(require("../../../utils/is-product-form-template-system-enabled")),format_product_error_1=require("../../../utils/format-product-error");function ProductDetailsSectionDescriptionBlockEdit({attributes:e,clientId:t,context:{selectedTab:o}}){const r=(0,block_templates_1.useWooBlockProps)(e),{getProductErrorMessageAndProps:n}=(0,use_error_handler_1.useErrorHandler)(),{productTemplates:c,productTemplate:a}=(0,data_1.useSelect)((e=>{const{getEditorSettings:t}=e("core/editor");return t()}),[]),[i,l]=c.reduce((([e,t],o)=>(o.isSelectableByUser&&(o.layoutTemplateId?e.push(o):t.push(o)),[e,t])),[[],[]]),s=(0,core_data_1.useEntityId)("postType","product"),[_]=(0,core_data_1.useEntityProp)("postType","product","status"),{validate:d}=(0,validation_context_1.useValidations)(),{editEntityRecord:u,saveEditedEntityRecord:m,saveEntityRecord:p}=(0,data_1.useDispatch)("core"),{createSuccessNotice:f,createErrorNotice:w}=(0,data_1.useDispatch)("core/notices"),E=(0,data_1.useSelect)((e=>{const{getBlockRootClientId:o}=e("core/block-editor");return o(t)}),[t]),[y,g]=(0,element_1.useState)(),h=(0,data_1.useSelect)((e=>(0,is_product_form_template_system_enabled_1.default)()&&e("core").getEntityRecords("postType","product_form",{per_page:-1})||[]),[]),{isSaving:v}=(0,data_1.useSelect)((e=>{const{isSavingEntityRecord:t}=e("core");return{isSaving:t("postType","product",s)}}),[s]);if(E)return(0,element_1.createElement)(block_slot_fill_1.BlockFill,{name:"section-description",slotContainerBlockName:"woocommerce/product-section"},(0,element_1.createElement)("div",{...r},(0,element_1.createElement)("p",null,(0,element_1.createInterpolateElement)((0,i18n_1.__)("This is a <ProductTemplate />.","woocommerce"),{ProductTemplate:(0,element_1.createElement)("span",null,a?.title?.toLowerCase())})),(0,element_1.createElement)(components_1.Dropdown,{focusOnMount:!0,popoverProps:{placement:"bottom-start"},renderToggle:({isOpen:e,onToggle:t})=>(0,element_1.createElement)(components_1.Button,{"aria-expanded":e,variant:"link",onClick:S(e,t)},(0,element_1.createElement)("span",null,(0,i18n_1.__)("Change product type","woocommerce"))),renderContent:({onClose:e})=>(0,element_1.createElement)("div",{className:"wp-block-woocommerce-product-details-section-description__dropdown components-dropdown-menu__menu"},(0,element_1.createElement)(components_1.MenuGroup,null,i.map(P(e))),(0,is_product_form_template_system_enabled_1.default)()&&(0,element_1.createElement)(components_1.MenuGroup,null,h.map((t=>(0,element_1.createElement)(components_1.MenuItem,{key:t.id,icon:k("external"),info:t.excerpt.raw,iconPosition:"left",onClick:e},t.title.rendered)))),l.length>0&&(0,element_1.createElement)(components_1.MenuGroup,null,(0,element_1.createElement)(components_1.Dropdown,{popoverProps:{placement:"right-start"},renderToggle:({isOpen:e,onToggle:t})=>(0,element_1.createElement)(components_1.MenuItem,{"aria-expanded":e,icon:k("chevronRight"),iconPosition:"right",onClick:t},(0,element_1.createElement)("span",null,(0,i18n_1.__)("More","woocommerce"))),renderContent:()=>(0,element_1.createElement)("div",{className:"wp-block-woocommerce-product-details-section-description__dropdown components-dropdown-menu__menu"},(0,element_1.createElement)(components_1.MenuGroup,null,l.map(P(e))))})))}),Boolean(y)&&(0,element_1.createElement)(components_1.Modal,{title:(0,i18n_1.__)("Change product type?","woocommerce"),className:"wp-block-woocommerce-product-details-section-description__modal",onRequestClose:()=>{g(void 0)}},(0,element_1.createElement)("p",null,(0,element_1.createElement)("b",null,(0,i18n_1.__)("This product type isn’t supported by the updated product editing experience yet.","woocommerce"))),(0,element_1.createElement)("p",null,(0,i18n_1.__)("You’ll be taken to the classic editing screen that isn’t optimized for commerce but offers advanced functionality and supports all extensions.","woocommerce")),(0,element_1.createElement)("div",{className:"wp-block-woocommerce-product-details-section-description__modal-actions"},(0,element_1.createElement)(components_1.Button,{variant:"secondary","aria-disabled":v,onClick:()=>{v||g(void 0)}},(0,i18n_1.__)("Cancel","woocommerce")),(0,element_1.createElement)(components_1.Button,{variant:"primary",isBusy:v,"aria-disabled":v,onClick:async function(){try{if(v)return;const{id:e,productData:t}=y;await d(t);const o=await m("postType","product",s,{throwOnError:!0})??{id:s},r=t?.meta_data??[];await p("postType","product",{...o,...t,meta_data:[...r,{key:"_product_template_id",value:e}]},{throwOnError:!0}),f((0,i18n_1.__)("Product type changed.","woocommerce")),(0,tracks_1.recordEvent)("product_template_changed",{source:constants_1.TRACKS_SOURCE,template:e}),window.location.href=(0,navigation_1.getNewPath)({},`/product/${s}`)}catch(e){const{message:t,errorProps:r}=await n((0,format_product_error_1.formatProductError)(e,_),o);w(t,r)}}},(0,i18n_1.__)("Change","woocommerce"))))));function b(e,t){return async function(){try{if((0,tracks_1.recordEvent)("product_template_selector_selected",{source:constants_1.TRACKS_SOURCE,selected_template:e.id,unsupported_template:!e.layoutTemplateId}),!e.layoutTemplateId)return g(e),void t();await d(e.productData);const o=e.productData.meta_data??[];await u("postType","product",s,{...e.productData,meta_data:[...o,{key:"_product_template_id",value:e.id}]}),await m("postType","product",s,{throwOnError:!0}),f((0,i18n_1.__)("Product type changed.","woocommerce")),(0,tracks_1.recordEvent)("product_template_changed",{source:constants_1.TRACKS_SOURCE,template:e.id})}catch(e){const{message:t,errorProps:r}=await n((0,format_product_error_1.formatProductError)(e,_),o);w(t,r)}t()}}function k(e,t){if(!e)return;const{Icon:o}=icons;let r;if(/^https?:\/\//.test(e))r=(0,element_1.createElement)("img",{src:e,alt:t});else{if(!(e in icons)&&!(e in wooIcons))return;r=icons[e]||wooIcons[e]}return(0,element_1.createElement)(o,{icon:r,size:24})}function P(e){return function(t){const o=a?.id===t.id;return(0,element_1.createElement)(components_1.MenuItem,{key:t.id,info:t.description??void 0,isSelected:o,icon:o?k("check"):k(t.icon,t.title),iconPosition:"left",role:"menuitemradio",onClick:b(t,e),className:(0,clsx_1.default)({"components-menu-item__button--selected":o})},t.title)}}function S(e,t){return function(){t(),e||(0,tracks_1.recordEvent)("product_template_selector_open",{source:constants_1.TRACKS_SOURCE,supported_templates:i.map((e=>e.id)),unsupported_template:l.map((e=>e.id))})}}}