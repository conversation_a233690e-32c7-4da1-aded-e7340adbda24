"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.ImageBlockEdit=ImageBlockEdit;const i18n_1=require("@wordpress/i18n"),components_1=require("@wordpress/components"),data_1=require("@wordpress/data"),clsx_1=__importDefault(require("clsx")),element_1=require("@wordpress/element"),icons_1=require("@wordpress/icons"),block_templates_1=require("@woocommerce/block-templates"),components_2=require("@woocommerce/components"),tracks_1=require("@woocommerce/tracks"),core_data_1=require("@wordpress/core-data"),place_holder_1=require("./place-holder"),block_slot_fill_1=require("../../../components/block-slot-fill"),map_upload_image_to_image_1=require("../../../utils/map-upload-image-to-image");function ImageBlockEdit({attributes:e,context:r}){const{property:o,multiple:a}=e,[t,i]=(0,core_data_1.useEntityProp)("postType",r.postType,o),[l,n]=(0,element_1.useState)(!1),[_,c]=(0,element_1.useState)(!1),[m,s]=(0,element_1.useState)(null),d=(0,block_templates_1.useWooBlockProps)(e,{className:(0,clsx_1.default)({"has-images":Array.isArray(t)?t.length>0:Boolean(t)})}),{createErrorNotice:p}=(0,data_1.useDispatch)("core/notices");function u(e){return function(r){if((0,tracks_1.recordEvent)(e),Array.isArray(r)){const e=r.filter((e=>e.id)).map((e=>({id:e.id,name:e.title,src:e.url,alt:e.alt})));r[0]?.id&&i([...t,...e])}else r.id&&i((0,map_upload_image_to_image_1.mapUploadImageToImage)(r))}}const g=null!==t&&(!Array.isArray(t)||t.length>0);return(0,element_1.createElement)("div",{...d},(0,element_1.createElement)("div",{className:"woocommerce-product-form__image-drop-zone"},l?(0,element_1.createElement)("div",{className:"woocommerce-product-form__remove-image-drop-zone"},(0,element_1.createElement)("span",null,(0,element_1.createElement)(icons_1.Icon,{icon:icons_1.trash,size:20,className:"icon-control"}),(0,i18n_1.__)("Drop here to remove","woocommerce")),(0,element_1.createElement)(components_1.DropZone,{onHTMLDrop:()=>c(!0),onDrop:()=>c(!0),label:(0,i18n_1.__)("Drop here to remove","woocommerce")})):(0,element_1.createElement)(block_slot_fill_1.SectionActions,null,(0,element_1.createElement)("div",{className:"woocommerce-product-form__media-uploader"},(0,element_1.createElement)(components_2.MediaUploader,{value:Array.isArray(t)?t.map((({id:e})=>e)):t?.id??void 0,multipleSelect:!!a&&"add",maxUploadFileSize:window.productBlockEditorSettings?.maxUploadFileSize,onError:function(e){p((0,i18n_1.sprintf)((0,i18n_1.__)("Error uploading image:%1$s%2$s","woocommerce"),"\n",e.message))},onFileUploadChange:u("product_images_add_via_file_upload_area"),onMediaGalleryOpen:()=>{(0,tracks_1.recordEvent)("product_images_media_gallery_open")},onSelect:function(e){if((0,tracks_1.recordEvent)("product_images_add_via_media_library"),Array.isArray(e)){const r=e.map(map_upload_image_to_image_1.mapUploadImageToImage).filter((e=>null!==e));i(r)}else i((0,map_upload_image_to_image_1.mapUploadImageToImage)(e))},onUpload:u("product_images_add_via_drag_and_drop_upload"),label:"",buttonText:(0,i18n_1.__)("Choose an image","woocommerce")})))),g?(0,element_1.createElement)(components_2.ImageGallery,{allowDragging:!1,onDragStart:function(e){if(Array.isArray(t)){const{id:r,dataset:o}=e.target;if(r)s(parseInt(r,10));else if(o?.index){const e=parseInt(o.index,10);s(t[e]?.id??null)}n((e=>!e))}},onDragEnd:function(){Array.isArray(t)&&(_&&m&&((0,tracks_1.recordEvent)("product_images_remove_image_button_click"),i(t.filter((e=>e.id!==m))),c(!1),s(null)),n((e=>!e)))},onOrderChange:function(e){if(Array.isArray(t)){const r=t.reduce(((e,r)=>({...e,[`${r.id}`]:r})),{}),o=e.filter((e=>e?.props?.id in r)).map((e=>r[e?.props?.id]));(0,tracks_1.recordEvent)("product_images_change_image_order_via_image_gallery"),i(o)}},onReplace:function({replaceIndex:e,media:r}){if((0,tracks_1.recordEvent)("product_images_replace_image_button_click"),Array.isArray(t)){if(t.some((e=>r.id===e.id)))return;const o=(0,map_upload_image_to_image_1.mapUploadImageToImage)(r);if(o){const r=[...t];r[e]=o,i(r)}}else i((0,map_upload_image_to_image_1.mapUploadImageToImage)(r))},onRemove:function({removedItem:e}){if((0,tracks_1.recordEvent)("product_images_remove_image_button_click"),Array.isArray(t)){const r=t.filter((r=>String(r.id)!==e.props.id));i(r)}else i(null)},onSelectAsCover:()=>(0,tracks_1.recordEvent)("product_images_select_image_as_cover_button_click")},(Array.isArray(t)?t:[t]).map(((e,r)=>(0,element_1.createElement)(components_2.ImageGalleryItem,{key:e.id,alt:e.alt,src:e.src,id:`${e.id}`,isCover:a&&0===r})))):(0,element_1.createElement)(place_holder_1.PlaceHolder,{multiple:a}))}