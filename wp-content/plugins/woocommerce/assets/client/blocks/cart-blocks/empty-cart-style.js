"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[4857],{55107:(e,c,l)=>{l.r(c),l.d(c,{default:()=>a});var s=l(2328),n=l(86087),t=l(43757),o=(l(34687),l(10790));const a=({children:e,className:c})=>{const{cartItems:l,cartIsLoading:a}=(0,s.V)();return(0,n.useEffect)((()=>{0!==l.length||a||(0,t.Pt)("wc-blocks_render_blocks_frontend",{element:document.body.querySelector(".wp-block-woocommerce-cart")})}),[a,l]),a||0!==l.length?null:(0,o.jsx)("div",{className:c,children:e})}}}]);