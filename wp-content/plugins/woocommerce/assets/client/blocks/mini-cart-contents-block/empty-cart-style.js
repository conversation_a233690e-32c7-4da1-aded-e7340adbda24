"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[149],{1186:(c,e,s)=>{s.r(e),s.d(e,{default:()=>t});var l=s(2328),a=s(86087),n=s(10790);const t=({children:c,className:e})=>{const{cartItems:s,cartIsLoading:t}=(0,l.V)(),r=(0,a.useRef)(null);return(0,a.useEffect)((()=>{0!==s.length||t||r.current?.focus()}),[s,t]),t||s.length>0?null:(0,n.jsx)("div",{tabIndex:-1,ref:r,className:e,children:(0,n.jsx)("div",{className:"wc-block-mini-cart__empty-cart-wrapper",children:c})})}}}]);