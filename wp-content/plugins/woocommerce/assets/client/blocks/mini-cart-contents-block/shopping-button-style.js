"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[2264],{80371:(t,e,o)=>{o.d(e,{p:()=>a});var n=o(4921),s=o(73993),r=o(70219),l=o(70017);const a=t=>{const e=(t=>{const e=(0,s.isObject)(t)?t:{style:{}};let o=e.style;return(0,s.isString)(o)&&(o=JSON.parse(o)||{}),(0,s.isObject)(o)||(o={}),{...e,style:o}})(t),o=(0,l.BK)(e),a=(0,l.aR)(e),c=(0,l.fo)(e),i=(0,r.x)(e);return{className:(0,n.A)(i.className,o.className,a.className,c.className),style:{...i.style,...o.style,...a.style,...c.style}}}},70219:(t,e,o)=>{o.d(e,{x:()=>s});var n=o(73993);const s=t=>{const e=(0,n.isObject)(t.style.typography)?t.style.typography:{},o=(0,n.isString)(e.fontFamily)?e.fontFamily:"";return{className:t.fontFamily?`has-${t.fontFamily}-font-family`:o,style:{fontSize:t.fontSize?`var(--wp--preset--font-size--${t.fontSize})`:e.fontSize,fontStyle:e.fontStyle,fontWeight:e.fontWeight,letterSpacing:e.letterSpacing,lineHeight:e.lineHeight,textDecoration:e.textDecoration,textTransform:e.textTransform}}}},70017:(t,e,o)=>{o.d(e,{BK:()=>i,aR:()=>u,fo:()=>y});var n=o(4921),s=o(67356),r=o(49786),l=o(73993);function a(t={}){const e={};return(0,r.getCSSRules)(t,{selector:""}).forEach((t=>{e[t.key]=t.value})),e}function c(t,e){return t&&e?`has-${(0,s.c)(e)}-${t}`:""}function i(t){const{backgroundColor:e,textColor:o,gradient:s,style:r}=t,i=c("background-color",e),u=c("color",o),y=function(t){if(t)return`has-${t}-gradient-background`}(s),g=y||r?.color?.gradient;return{className:(0,n.A)(u,y,{[i]:!g&&!!i,"has-text-color":o||r?.color?.text,"has-background":e||r?.color?.background||s||r?.color?.gradient,"has-link-color":(0,l.isObject)(r?.elements?.link)?r?.elements?.link?.color:void 0}),style:a({color:r?.color||{}})}}function u(t){const e=t.style?.border||{};return{className:function(t){const{borderColor:e,style:o}=t,s=e?c("border-color",e):"";return(0,n.A)({"has-border-color":!!e||!!o?.border?.color,[s]:!!s})}(t),style:a({border:e})}}function y(t){return{className:void 0,style:a({spacing:t.style?.spacing||{}})}}},83144:(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var n=o(78331),s=o(89874),r=o(80371),l=o(4921),a=o(95940),c=o(22805),i=o(10790);const u=({className:t,startShoppingButtonLabel:e,style:o,textColor:u,backgroundColor:y})=>{const g=(0,r.p)({style:o,textColor:u,backgroundColor:y});return n.Jn?(0,i.jsx)("div",{className:"wp-block-button has-text-align-center",children:(0,i.jsx)(s.A,{className:(0,l.A)(t,g.className,"wc-block-mini-cart__shopping-button"),style:g.style,variant:(0,c.I)(t,"contained"),href:n.Jn,children:e||a.g})}):null}}}]);