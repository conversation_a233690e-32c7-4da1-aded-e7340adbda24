function isValidFormattedNumber(e,n){if(!e||"string"!=typeof e||!n||"object"!=typeof n)return!1;var t=n.decimalSeparator||".",o=n.thousandSeparator||",",i=o.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),s=t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),d=new RegExp("([0-9,.' "+s+i+"]+)","g");const a=(e.match(d)||[]).map(e=>e.trim()).filter(e=>""!==e);if(0===a.length){return/^\[([a-zA-Z0-9_"'= ]+)\]/.test(e)}return a.every(e=>{if(!e||0===e.length||!e[0].match(/\d/))return!1;const n=e.match(/([^0-9])+/g);if(!n)return!0;const i=n.pop();if(n.length>0){if(new Set(n).size>1)return!1;if(n[0]!==o)return!1}if(i.trim()!==t.trim()){if(i.trim()!==o.trim())return!1;const n=e.split(i).pop();if(!n||3!==n.length||!/^\d{3}$/.test(n))return!1}return!0})}"undefined"!=typeof module&&module.exports?module.exports={isValidFormattedNumber:isValidFormattedNumber}:"function"==typeof define&&define.amd?define([],function(){return{isValidFormattedNumber:isValidFormattedNumber}}):window.WCNumberValidation={isValidFormattedNumber:isValidFormattedNumber},function(e,n,t,o){e(function(){var i=e(".wc-shipping-zone-methods"),s=e(".wc-shipping-zone-method-rows"),d=e(".wc-shipping-zone-method-save"),a=t.template("wc-shipping-zone-method-row"),c=t.template("wc-shipping-zone-method-row-blank"),r=Backbone.Model.extend({changes:{},logChanges:function(e){var n=this.changes||{};_.each(e.methods,function(e,t){n.methods=n.methods||{methods:{}},n.methods[t]=_.extend(n.methods[t]||{instance_id:t},e)}),"undefined"!=typeof e.zone_name&&(n.zone_name=e.zone_name),"undefined"!=typeof e.zone_locations&&(n.zone_locations=e.zone_locations),"undefined"!=typeof e.zone_postcodes&&(n.zone_postcodes=e.zone_postcodes),this.changes=n,this.trigger("change:methods")},save:function(){var t=_.clone(this.changes);_.has(t,"zone_locations")&&_.isEmpty(t.zone_locations)&&(t.zone_locations=[""]),e.post(o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_methods_save_changes",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,changes:t,zone_id:n.zone_id},this.onSaveResponse,"json")},onSaveResponse:function(e,t){"success"===t&&(e.success?(e.data.zone_id!==n.zone_id&&(n.zone_id=e.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+e.data.zone_id)),h.set("methods",e.data.methods),h.trigger("change:methods"),h.changes={},h.trigger("saved:methods"),window.onbeforeunload=null):window.alert(n.strings.save_failed))}}),l=Backbone.View.extend({rowTemplate:a,initialize:function(){this.listenTo(this.model,"change:methods",this.setUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.clearUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.render),this.listenTo(this.model,"rerender",this.render),s.on("change",{view:this},this.updateModelOnChange),s.on("sortupdate",{view:this},this.updateModelOnSort),e(window).on("beforeunload",{view:this},this.unloadConfirmation),d.on("click",{view:this},this.onSubmit),e(document.body).on("input change","#zone_name, #zone_locations, #zone_postcodes",{view:this},this.onUpdateZone),e(document.body).on("click",".wc-shipping-zone-method-settings",{view:this},this.onConfigureShippingMethod),e(document.body).on("click",".wc-shipping-zone-add-method",{view:this},this.onAddShippingMethod),e(document.body).on("wc_backbone_modal_response",this.onConfigureShippingMethodSubmitted),e(document.body).on("wc_region_picker_update",this.onUpdateZoneRegionPicker),e(document.body).on("wc_backbone_modal_next_response",this.onAddShippingMethodSubmitted),e(document.body).on("wc_backbone_modal_before_remove",this.onCloseConfigureShippingMethod),e(document.body).on("wc_backbone_modal_back_response",this.onConfigureShippingMethodBack),e(document.body).on("click",".wc-shipping-zone-postcodes-toggle",this.onTogglePostcodes),e(document.body).on("wc_backbone_modal_validation",{view:this},this.validateFormArguments),e(document.body).on("wc_backbone_modal_loaded",{view:this},this.onModalLoaded)},onUpdateZoneRegionPicker:function(e){var n=e.detail,t={};t.zone_locations=n,p.model.set("zone_locations",n),p.model.logChanges(t)},onUpdateZone:function(n){var t=n.data.view,o=t.model,i=e(this).val(),s=e(n.target).data("attribute"),d={};n.preventDefault(),d[s]=i,o.set(s,i),o.logChanges(d),t.render()},block:function(){e(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e(this.el).unblock()},render:function(){var t=_.indexBy(this.model.get("methods"),"instance_id"),o=this.model.get("zone_name"),i=this;e(".wc-shipping-zone-name").text(o||n.strings.default_zone_name),this.$el.empty(),this.unblock(),_.size(t)?(t=_.sortBy(t,function(e){return parseInt(e.method_order,10)}),e.each(t,function(e,t){"yes"===t.enabled?t.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--enabled">'+n.strings.yes+"</span>":t.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--disabled">'+n.strings.no+"</span>",i.$el.append(i.rowTemplate(t));var o=i.$el.find('tr[data-id="'+t.instance_id+'"]');if(!t.has_settings){o.find(".wc-shipping-zone-method-title > a").replaceWith("<span>"+o.find(".wc-shipping-zone-method-title > a").text()+"</span>");var s=o.find(".wc-shipping-zone-method-delete");o.find(".wc-shipping-zone-method-title .row-actions").empty().html(s)}}),this.$el.find(".wc-shipping-zone-method-delete").on("click",{view:this},this.onDeleteRow),this.$el.find(".wc-shipping-zone-method-enabled a").on("click",{view:this},this.onToggleEnabled)):i.$el.append(c),this.initTooltips()},initTooltips:function(){e("#tiptip_holder").removeAttr("style"),e("#tiptip_arrow").removeAttr("style"),e(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:50})},onSubmit:function(e){d.addClass("is-busy"),e.data.view.block(),e.data.view.model.save(),e.preventDefault()},onDeleteRow:function(t){var i=t.data.view,s=i.model,d=_.indexBy(s.get("methods"),"instance_id"),a={},c=e(this).closest("tr").data("id");t.preventDefault(),window.confirm(n.strings.delete_shipping_method_confirmation)&&(p.block(),e.post({url:o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_remove_method",data:{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:c,zone_id:n.zone_id},success:function({data:e}){delete d[c],a.methods=a.methods||e.methods,s.set("methods",d),s.logChanges(a),i.clearUnloadConfirmation(),i.render(),p.unblock()},error:function(e,t,o){window.alert(n.strings.remove_method_failed),p.unblock()},dataType:"json"}))},onToggleEnabled:function(n){var t=n.data.view,o=e(n.target),i=t.model,s=_.indexBy(i.get("methods"),"instance_id"),d=o.closest("tr").data("id"),a="yes"===o.closest("tr").data("enabled")?"no":"yes",c={};n.preventDefault(),s[d].enabled=a,c.methods=c.methods||{methods:{}},c.methods[d]=_.extend(c.methods[d]||{},{enabled:a}),i.set("methods",s),i.logChanges(c),t.render()},setUnloadConfirmation:function(){this.needsUnloadConfirm=!0,d.prop("disabled",!1),d.removeClass("is-busy")},clearUnloadConfirmation:function(){this.needsUnloadConfirm=!1,d.attr("disabled","disabled")},unloadConfirmation:function(e){if(e.data.view.needsUnloadConfirm)return e.returnValue=n.strings.unload_confirmation_msg,window.event.returnValue=n.strings.unload_confirmation_msg,n.strings.unload_confirmation_msg},updateModelOnChange:function(n){var t=n.data.view.model,o=e(n.target),i=o.closest("tr").data("id"),s=o.data("attribute"),d=o.val(),a=_.indexBy(t.get("methods"),"instance_id"),c={};a[i][s]!==d&&(c.methods[i]={},c.methods[i][s]=d,a[i][s]=d),t.logChanges(c)},updateModelOnSort:function(e){var n=e.data.view.model,t=_.indexBy(n.get("methods"),"instance_id"),o={};_.each(t,function(e){var n=parseInt(e.method_order,10),s=parseInt(i.find('tr[data-id="'+e.instance_id+'"]').index()+1,10);n!==s&&(t[e.instance_id].method_order=s,o.methods=o.methods||{methods:{}},o.methods[e.instance_id]=_.extend(o.methods[e.instance_id]||{},{method_order:s}))}),_.size(o)&&n.logChanges(o)},onConfigureShippingMethod:function(n){var t=e(this).closest("tr").data("id"),o=n.data.view.model,i=_.indexBy(o.get("methods"),"instance_id")[t];if(!i.settings_html)return!0;n.preventDefault(),i.settings_html=p.reformatSettingsHTML(i.settings_html),e(this).WCBackboneModal({template:"wc-modal-shipping-method-settings",variable:{instance_id:t,method:i,status:"existing"},data:{instance_id:t,method:i,status:"existing"}}),p.highlightOnFocus(".wc-shipping-modal-price"),e(document.body).trigger("init_tooltips")},unformatShippingMethodNumericValues:function(e){if(!window.wc.wcSettings.CURRENCY)return e;const n=window.wc.wcSettings.CURRENCY,t=["woocommerce_free_shipping_min_amount","woocommerce_flat_rate_cost","woocommerce_flat_rate_no_class_cost"];return Object.keys(e).forEach(o=>{if(t.includes(o)||o.startsWith("woocommerce_flat_rate_class_cost_")){const t=e[o];try{const s=window.wc.currency.unformatLocalisedMonetaryValue(n,t);e[o]=s}catch(i){return}}}),e},onConfigureShippingMethodSubmitted:function(t,i,s){"wc-modal-shipping-method-settings"===i&&(p.block(),e.post(o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_methods_save_settings",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:s.instance_id,data:p.unformatShippingMethodNumericValues(s)},function(t,o){"success"===o&&t.success?(e("table.wc-shipping-zone-methods").parent().find("#woocommerce_errors").remove(),t.data.errors.length>0&&p.showErrors(t.data.errors),_.size(p.model.changes)?p.model.save():p.model.onSaveResponse(t,o)):(window.alert(n.strings.save_failed),p.unblock())},"json"))},onConfigureShippingMethodBack:function(e,n){"wc-modal-shipping-method-settings"===n&&p.onAddShippingMethod(e)},showErrors:function(n){var t='<div id="woocommerce_errors" class="error notice is-dismissible">';e(n).each(function(e,n){t=t+"<p>"+n+"</p>"}),t+="</div>",e("table.wc-shipping-zone-methods").before(t)},highlightOnFocus:function(n){e(n).focus(function(){e(this).select()})},onAddShippingMethod:function(t){t.preventDefault(),e(this).WCBackboneModal({template:"wc-modal-add-shipping-method",variable:{zone_id:n.zone_id}}),e(".wc-shipping-zone-method-selector select").trigger("change"),e(".wc-shipping-zone-method-input input").change(function(){const n=e(".wc-shipping-zone-method-input input:checked").attr("id"),t=e(`#${n}-description`);e(".wc-shipping-zone-method-input-help-text").css("display","none"),t.css("display","block")})},reformatSettingsHTML:function(e){return[this.replaceHTMLTables,this.moveAdvancedCostsHelpTip,this.moveHTMLHelpTips,this.addCurrencySymbol].reduce((e,n)=>n(e),e)},moveAdvancedCostsHelpTip:function(n){const t=e(n),o=t.find("#wc-shipping-advanced-costs-help-text");o.addClass("wc-shipping-zone-method-fields-help-text");const i=t.find("#woocommerce_flat_rate_cost").closest("fieldset");return o.appendTo(i),t.prop("outerHTML")},addCurrencySymbol:function(n){if(!window.wc.wcSettings.CURRENCY||!window.wc.currency.localiseMonetaryValue)return n;const t=e(n),o=t.find(".wc-shipping-modal-price"),i=window.wc.wcSettings.CURRENCY,{symbol:s,symbolPosition:d}=i;return o.addClass(`wc-shipping-currency-size-${s.length}`),o.addClass(`wc-shipping-currency-position-${d}`),o.before(`<div class="wc-shipping-zone-method-currency wc-shipping-currency-position-${d}">${s}</div>`),o.each(n=>{const t=e(o[n]),s=t.attr("value"),d=window.wc.currency.localiseMonetaryValue(i,s);t.attr("value",d)}),t.prop("outerHTML")},moveHTMLHelpTips:function(n){const t=["woocommerce_flat_rate_cost","woocommerce_flat_rate_no_class_cost","woocommerce_flat_rate_class_cost_"],o=e(n),i=o.find("label");return i.each(n=>{const s=e(i[n]),d=s.find(".woocommerce-help-tip");if(0===d.length)return;const a=s.attr("for");if(t.some(e=>a.includes(e))){o.find(`label[for=${a}] span.woocommerce-help-tip`).addClass("wc-shipping-visible-help-text")}else{if("woocommerce_free_shipping_ignore_discounts"===a){o.find(`#${a}`).closest("fieldset").find("label").append(d)}else{const e=d.data("tip"),n=o.find(`#${a}`).closest("fieldset");n.length&&0===n.find(".wc-shipping-zone-method-fields-help-text").length&&n.append(`<div class="wc-shipping-zone-method-fields-help-text">${e}</div>`)}"Coupons discounts"===s.text().trim()&&s.text("")}}),o.prop("outerHTML")},replaceHTMLTables:function(n){const t=e("<div>"+n+"</div>"),o=t.find("table.form-table");return o.each(n=>{const t=e(o[n]),i=e('<div class="wc-shipping-zone-method-fields" />');i.html(t.html()),t.replaceWith(i)}),t.prop("outerHTML")},onAddShippingMethodSubmitted:function(t,i,s,d){"wc-modal-add-shipping-method"===i&&(p.block(),e("#btn-next").addClass("is-busy"),e.post(o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_add_method",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,method_id:s.add_method_id,zone_id:n.zone_id},function(t,o){"success"===o&&t.success&&(t.data.zone_id!==n.zone_id&&(n.zone_id=t.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+t.data.zone_id)),p.model.set("methods",t.data.methods),d());var i=t.data.instance_id,s=t.data.methods[i];p.unblock(),s.settings_html?(s.settings_html=p.reformatSettingsHTML(s.settings_html),e(this).WCBackboneModal({template:"wc-modal-shipping-method-settings",variable:{instance_id:i,method:s,status:"new"},data:{instance_id:i,method:s,status:"new"}}),p.highlightOnFocus(".wc-shipping-modal-price")):(p.model.trigger("change:methods"),p.model.trigger("saved:methods")),e(document.body).trigger("init_tooltips")},"json"))},possiblyHideFreeShippingRequirements:function(n){if(Object.keys(n).includes("woocommerce_free_shipping_requires")){const t=null===n.woocommerce_free_shipping_requires||""===n.woocommerce_free_shipping_requires||"coupon"===n.woocommerce_free_shipping_requires,o=e("#woocommerce_free_shipping_requires").closest("fieldset"),i=o.nextAll("label"),s=o.nextAll("fieldset");i.each(n=>{e(i[n]).css("display",t?"none":"block")}),s.each(n=>{e(s[n]).css("display",t?"none":"block")})}},onModalLoaded:function(n,t){if("wc-modal-shipping-method-settings"===t){const t=e("#woocommerce_free_shipping_requires");if(t.length>0&&n.data.view.possiblyHideFreeShippingRequirements({woocommerce_free_shipping_requires:t.val()}),n.data.view.possiblyAddShippingClassLink(n),window.wc.wcSettings.CURRENCY&&window.wc.currency.localiseMonetaryValue){const n=window.wc.wcSettings.CURRENCY;e(".wc-shipping-modal-price").on("input",function(){const t=e(this).val();e(this).removeClass("wc-shipping-invalid-price"),e(this).siblings("span.wc-shipping-invalid-price-message").remove();const o=e(this).parents(".wc-backbone-modal-main");o.find("#btn-ok").removeAttr("disabled"),o.find(".wc-shipping-method-add-class-costs").show(),WCNumberValidation.isValidFormattedNumber(t,n)||(e(this).addClass("wc-shipping-invalid-price"),e('<span class="wc-shipping-zone-method-fields-help-text wc-shipping-invalid-price-message">'+shippingZoneMethodsLocalizeScript.strings.invalid_number_format+"</span>").insertAfter(this),o.find("#btn-ok").attr("disabled","disabled"),o.find(".wc-shipping-method-add-class-costs").hide())}),e(".wc-shipping-modal-price").on("blur",function(){const t=e(this).val(),o=window.wc.currency.localiseMonetaryValue(n,t);e(this).val(o)})}}},possiblyAddShippingClassLink:function(n){const t=e("article.wc-modal-shipping-method-settings"),o=t.data("shipping-classes-count"),i=(t.data("status"),t.data("id")),s=n.data.view.model;if("flat_rate"===_.indexBy(s.get("methods"),"instance_id")[i].id&&0===o){t.find(".wc-shipping-method-add-class-costs").css("display","block")}},validateFormArguments:function(e,n,t){if("wc-modal-add-shipping-method"===n){if(t.add_method_id){const e=document.getElementById("btn-next");e.disabled=!1,e.classList.remove("disabled")}}else"wc-modal-shipping-method-settings"===n&&e.data.view.possiblyHideFreeShippingRequirements(t)},onCloseConfigureShippingMethod:function(t,i,s,d){if("wc-modal-shipping-method-settings"===i){var a=e("#btn-ok").data();if(!d&&a&&"new"===a.status){p.block();var c=p,r=c.model,l=_.indexBy(r.get("methods"),"instance_id"),h={},m=s.instance_id;e.post({url:o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_remove_method",data:{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:m,zone_id:n.zone_id},success:function({data:e}){delete l[m],h.methods=h.methods||e.methods,r.set("methods",l),r.logChanges(h),c.clearUnloadConfirmation(),c.render(),p.unblock()},error:function(e,t,o){window.alert(n.strings.remove_method_failed),p.unblock()},dataType:"json"})}}},onTogglePostcodes:function(n){n.preventDefault();var t=e(this).closest("tr");t.find(".wc-shipping-zone-postcodes").show(),t.find(".wc-shipping-zone-postcodes-toggle").hide()}}),h=new r({methods:n.methods,zone_name:n.zone_name}),p=new l({model:h,el:s});p.render(),s.sortable({items:"tr",cursor:"move",axis:"y",handle:"td.wc-shipping-zone-method-sort",scrollSensitivity:40})})}(jQuery,shippingZoneMethodsLocalizeScript,wp,ajaxurl);