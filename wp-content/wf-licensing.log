[Sun, 31 Aug 2025 04:56:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:56:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:57:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:57:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:57:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:58:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 04:59:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:00:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:01:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:03:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:04:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:05:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:21:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:22:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:23:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:24:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:25:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:26:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:28:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 05:47:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:13:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:13:07 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:14:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:15:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:16:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:17:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:18:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:20:25 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:45:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:46:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:48:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:50:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:52:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:54:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 06:59:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:00:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:02:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:03:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:04:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:06:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:06:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:06:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:06:05 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:07:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:31:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 07:31:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:05:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:07:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:08:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:10:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:12:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:14:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:16:14 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:18:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:19:00 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:21:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:23:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:28:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:40:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:40:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:40:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:40:27 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:40:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:43:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:44:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:46:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:48:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:50:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:52:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:58:23 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 08:59:24 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:00:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:02:49 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:04:50 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:05:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:07:52 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:09:53 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:11:54 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 09:13:55 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:01:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:01:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:03:30 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:05:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:07:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:09:37 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:11:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:13:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:13:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:13:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:13:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:14:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:15:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:15:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:16:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:17:04 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:18:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:18:03 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:18:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:18:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:19:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:21:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:23:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:25:13 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:30:55 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:31:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:33:57 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:35:58 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:38:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 10:40:01 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:36:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:36:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:37:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:38:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:40:18 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:42:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:44:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 11:46:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:26 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:32 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:42 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:06:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:07:43 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:09:44 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:11:45 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:13:46 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:15:47 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:17:48 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:37:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Sun, 31 Aug 2025 12:37:10 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:56:40 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:56:51 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:56:56 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:02 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:06 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:57:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:58:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:58:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:59:12 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:59:28 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:59:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 04:59:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:00:33 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:01:34 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:03:35 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:05:36 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:07:37 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:09:38 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:44:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:46:08 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:48:09 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:50:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:52:11 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:53:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:54:16 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:56:17 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:58:19 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 05:58:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 06:00:20 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

[Mon, 01 Sep 2025 06:02:21 +0000] wpr: __construct
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'skip_hooks' => false,
  'debug' => true,
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
)
array (
  'prefix' => 'wpr',
  'licensing_servers' => 
  array (
    0 => 'https://dashboard.wpreset.com/api/',
  ),
  'version' => '2.05',
  'slug' => 'wp-reset',
  'basename' => 'wp-reset/wp-reset.php',
  'plugin_page' => 'tools_page_wp-reset',
  'plugin_file' => '/Users/<USER>/Documents/wordpress/notificationx/wp-content/plugins/wp-reset/wp-reset.php',
  'js_folder' => 'https://notificationx.test/wp-content/plugins/wp-reset//js/',
  'api_ver' => 'v1/',
  'valid_forever' => '2035-01-01',
  'unlimited_installs' => 99999,
  'debug' => true,
  'filesystem_initialized' => false,
)

